package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.MacDao;
import com.geeksec.ngbatis.service.MacService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Mac服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class MacServiceImpl implements MacService {

    @Autowired
    private MacDao macDao;

    /**
     * Mac关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getMacNebulaAssociationNext(GraphNextInfoCondition condition) {
        return macDao.listMacAllEdgeTypeAssociationNext(condition);
    }
}
