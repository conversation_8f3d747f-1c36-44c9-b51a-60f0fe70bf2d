package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.IpDao;
import com.geeksec.ngbatis.service.IpService;
import com.geeksec.ngbatis.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
*@description: Ip服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class IpServiceImpl implements IpService {

    @Autowired
    private IpDao ipDao;

    /**
     * IP关联查询
     */
    @Override
    public List<VertexEdgeVo> getIpNebulaAssociation(String ip){
        return ipDao.listIpAllEdgeTypeAssociation(ip);
    }

    /**
     * IP关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getIpNebulaAssociationNext(GraphNextInfoCondition condition){
        return ipDao.listIpAllEdgeTypeAssociationNext(condition);
    }

    /**
    * 根据app_server边类型进行IP聚合总数查询
    */
    @Override
    public List<IpCountVo> countIpByAppServerEdge(List<String> ips) {
        return ipDao.countIpByAppServerEdge(ips);
    }

    /**
     * 根据client_app边类型进行IP聚合总数查询
     */
    @Override
    public List<IpCountVo> countIpByClientAppEdge(List<String> ips) {
        return ipDao.countIpByClientAppEdge(ips);
    }

    /**
     * 根据ids查询ip详情
     */
    @Override
    public List<IpVo> listByIps(List<String> ips) {
        return ipDao.listByIps(ips);
    }

    /**
     * 根据ids查询标签
     */
    @Override
    public List<IpHasLabelVo> listHasLabelByIps(List<String> ips) {
        return ipDao.listHasLabelByIps(ips);
    }

    /**
     * 根据域名的边类型进行IP聚合总数查询
     */
    @Override
    public List<IpCountVo> countIpByDomainEdge(List<String> ips) {
        return ipDao.countIpByDomainEdge(ips);
    }

    /**
     * 根据证书的边类型进行IP聚合总数查询
     */
    @Override
    public List<IpCountVo> countIpByCertEdge(List<String> ips) {
        return ipDao.countIpByCertEdge(ips);
    }

    /**
     * 根据ids查询存在IP属性
     */
    @Override
    public List<ExistIpPropertiesVo> listExistIpProperties(List<String> ips) {
        return ipDao.listExistIpProperties(ips);
    }

    /**
     * 查询ip详情
     */
    @Override
    public Map<String, Object> getIpInfo(String ip) {
        return ipDao.getIpInfo(ip);
    }

    /**
     * 统计开放端口数
     */
    @Override
    public IpCountVo countAppServerByIp(String ip) {
        return ipDao.countAppServerByIp(ip);
    }

    /**
     * 统计访问端口数
     */
    @Override
    public IpCountVo countClientAppByIp(String ip) {
        return ipDao.countClientAppByIp(ip);
    }

    /**
     * 关联锚域名（parse_to）
     */
    @Override
    public IpRelatedDomainsVo listParseToRelatedDomainsByIp(String ip) {
        return ipDao.listParseToRelatedDomainsByIp(ip);
    }

    /**
     * 关联锚域名（server_ssl_connect_domain SSL应用 server_http_connect_domain HTTP应用）
     */
    @Override
    public IpRelatedDomainsVo listServerRelatedDomainsByIp(String ip) {
        return ipDao.listServerRelatedDomainsByIp(ip);
    }

    /**
     * 根据domains统计所属锚域名数
     */
    @Override
    public Integer countFDomainNumByDomains(Set<String> domains) {
        return ipDao.countFDomainNumByDomains(domains);
    }

    /**
     * 根据id查询标签
     */
    @Override
    public List<Long> listHasLabelByIp(String ip) {
        return ipDao.listHasLabelByIp(ip);
    }

    /**
     * 修改备注
     */
    @Override
    public void updateRemark(String type, String id, String remark) {
        ipDao.updateRemark(type,id,remark);
    }



}
