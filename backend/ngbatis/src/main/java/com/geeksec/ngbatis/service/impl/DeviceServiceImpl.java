package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.DeviceDao;
import com.geeksec.ngbatis.service.DeviceService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Device服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    private DeviceDao deviceDao;

    /**
     * Device关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getDeviceNebulaAssociationNext(GraphNextInfoCondition condition) {
        return deviceDao.listDeviceAllEdgeTypeAssociationNext(condition);
    }
}
