package com.geeksec.ngbatis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.util.Map;
import java.util.Objects;

/**
*@description: 点关联关系点Vo
*@author: shiwenxu
*@createtime: 2023/8/30 11:28
**/
@Data
@JsonPropertyOrder({ "vInfo", "num", "lv", "label", "id", "type", "status"})
public class VertexAssociationVertexNextVo {

    private String num = "";

    private Long lv = 0L;

    private String label = "";

    private String id = "";

    private String type = "";

    private String status = "";

    @JsonProperty("v_info")
    private Map<String,String> vInfo;

    @Override
    public boolean equals(Object obj) {
        if(this == obj) {
            return true;
        }
        if(obj instanceof VertexAssociationVertexNextVo) {
            VertexAssociationVertexNextVo p  = (VertexAssociationVertexNextVo)obj;
            if((this.getNum().equals(p.getNum()))  && (this.getLv().equals(p.getLv()))
                    && (this.getId().equals(p.getId()))
                    && (this.getType().equals(p.getType())) && (this.getStatus().equals(p.getStatus()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(num,lv,id,type,status);
    }
}

