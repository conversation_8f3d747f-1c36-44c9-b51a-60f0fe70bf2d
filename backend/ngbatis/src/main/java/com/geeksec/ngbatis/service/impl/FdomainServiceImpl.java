package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.FdomainDao;
import com.geeksec.ngbatis.service.FdomainService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Fdomain服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class FdomainServiceImpl implements FdomainService {

    @Autowired
    private FdomainDao fdomainDao;

    /**
     * Fdomain关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getFdomainNebulaAssociationNext(GraphNextInfoCondition condition) {
        return fdomainDao.listFdomainAllEdgeTypeAssociationNext(condition);
    }
}
