<mapper namespace="com.geeksec.ngbatis.repository.OsDao">

    <select id="listOsAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (OS:OS)
        WHERE id(OS) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "OS" AS fromType,
        0 AS fromBlackList,
        OS.OS.os_name AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'ua_belong_os'){
                UNION ALL
                MATCH
                (OS:OS)-[ua_belong_os:ua_belong_os]-(UA:UA)
                WHERE
                id(OS) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "OS" AS toType,
                0 AS toBlackList,
                OS.OS.os_name AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(UA) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>