<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.push.dao.normal.DiskTypeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.push.entity.DiskType">
        <id column="id" property="id" />
        <result column="state" property="state" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, state, start_time, end_time
    </sql>

    <select id="getOrderIdOne" resultType="com.geeksec.push.entity.DiskType">
        select * from tb_disk_type order by id desc limit 1;
    </select>

</mapper>
