package com.geeksec.push.dao.normal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.push.entity.TaskStatistic;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface TaskStatisticDao extends BaseMapper<TaskStatistic> {

    /**
     * 用今日24点的时间获取今日
     * @param startTime
     * @param endTime
     * @param taskId
     * @return
     */
    Long getTodayBps(@Param("startTime") Integer startTime,@Param("endTime") Integer endTime,@Param("taskId") Integer taskId);
}
