version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: nta-task-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: nta
      MYSQL_USER: nta_user
      MYSQL_PASSWORD: nta_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Zookeeper (Kafka 依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: nta-task-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  # Kafka 消息队列
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: nta-task-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      timeout: 20s
      retries: 10

  # Kafka UI (可选，用于管理和监控)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: nta-task-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092

  # Redis (如果需要缓存)
  redis:
    image: redis:7-alpine
    container_name: nta-task-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # 任务服务应用
  task-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nta-task-service
    depends_on:
      mysql:
        condition: service_healthy
      kafka:
        condition: service_healthy
    ports:
      - "8087:8087"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: nta
      MYSQL_USERNAME: nta_user
      MYSQL_PASSWORD: nta_password
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    volumes:
      - /tmp:/tmp
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/api/task/pcap-import/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: nta-task-network
