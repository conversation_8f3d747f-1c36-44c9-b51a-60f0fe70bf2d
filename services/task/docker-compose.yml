version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: nta-task-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: nta
      MYSQL_USER: nta_user
      MYSQL_PASSWORD: nta_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10



  # Redis (如果需要缓存)
  redis:
    image: redis:7-alpine
    container_name: nta-task-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # 任务服务应用
  task-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nta-task-service
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8087:8087"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: nta
      MYSQL_USERNAME: nta_user
      MYSQL_PASSWORD: nta_password

    volumes:
      - /tmp:/tmp
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/api/task/pcap-import/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: nta-task-network
