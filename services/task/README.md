# NTA 3.0 任务管理服务 - PCAP导入管理器

## 概述

本模块是从 NTA 2.0 的 Python 版本 `pcap_import_manager.py` 迁移到 NTA 3.0 的 Java 版本，实现了 PCAP 文件导入的完整管理功能。

## 功能特性

### 核心功能
- **服务管理**: 自动管理多个离线 THD 服务实例
- **任务调度**: 智能分配空闲服务处理 PCAP 导入任务
- **规则同步**: 同步特征规则、过滤器规则和配置
- **文件处理**: 支持 PCAP、CAP、PCAPNG 文件格式
- **Kafka 集成**: 监听 Kafka 消息自动处理导入请求

### 技术特性
- **DDD 架构**: 采用领域驱动设计，清晰的分层架构
- **Spring Boot**: 基于 Spring Boot 3.0.7 构建
- **MyBatis-Plus**: 数据访问层使用 MyBatis-Plus
- **Kafka**: 异步消息处理
- **Docker**: 支持容器化部署

## 架构设计

```
src/main/java/com/geeksec/nta/task/
├── application/           # 应用服务层
│   ├── dto/              # 数据传输对象
│   └── service/          # 应用服务
├── domain/               # 领域层
│   ├── entity/           # 实体
│   ├── enums/            # 枚举
│   ├── repository/       # 仓储接口
│   ├── service/          # 领域服务接口
│   └── valueobject/      # 值对象
├── infrastructure/       # 基础设施层
│   ├── config/           # 配置类
│   ├── mapper/           # MyBatis Mapper
│   ├── messaging/        # 消息处理
│   ├── po/               # 持久化对象
│   ├── repository/       # 仓储实现
│   ├── service/          # 领域服务实现
│   └── util/             # 工具类
└── interfaces/           # 接口层
    └── rest/             # REST 控制器
```

## 配置说明

### 应用配置 (application.yml)

```yaml
# PCAP导入配置
pcap:
  import:
    service-ids: 11,12,13                                    # 可用服务ID列表
    base-conf-path: /conf                                    # 基础配置路径
    thd-env-path: /opt/GeekSec/th/bin/conf_pub/env          # THD环境文件路径
    service-path: /opt/GeekSec/th/bin/service               # 服务文件路径
    systemd-path: /etc/systemd/system                       # Systemd路径
    thd-root: /opt/GeekSec/th                               # THD根目录
    source-conf-path: /opt/GeekSec/task/thd_offline_conf    # 源配置路径

# Kafka配置
kafka:
  topics:
    offline-thd: offline_thd                                # PCAP导入主题
  consumer:
    group-id: pcap-import-group                             # 消费者组ID
```

### 数据库配置

执行 `src/main/resources/sql/init.sql` 创建必要的数据表：
- `tb_pcap_import_task`: PCAP导入任务表
- `tb_batch_offline_thd`: 批次离线THD关联表

## API 接口

### REST 接口

#### 1. 手动触发PCAP导入
```http
POST /api/task/pcap-import/process
Content-Type: application/json

{
  "taskId": 1001,
  "batchId": 2001,
  "inputDirs": ["/data/pcap/dir1", "/data/pcap/dir2"]
}
```

#### 2. 查询任务状态
```http
GET /api/task/pcap-import/status?taskId=1001&batchId=2001
```

#### 3. 查询待处理任务
```http
GET /api/task/pcap-import/pending
```

#### 4. 健康检查
```http
GET /api/task/pcap-import/health
```

### Kafka 消息格式

监听主题: `offline_thd`

消息格式:
```json
{
  "task_id": 1001,
  "batch_id": 2001,
  "input_dirs": ["/data/pcap/dir1", "/data/pcap/dir2"]
}
```

## 部署指南

### 1. 本地开发环境

```bash
# 启动 MySQL 和 Kafka
docker-compose up -d mysql kafka

# 运行应用
mvn spring-boot:run
```

### 2. Docker 部署

```bash
# 构建镜像
mvn clean package
docker build -t nta-task-service:3.0.0 .

# 运行容器
docker run -d \
  --name nta-task-service \
  -p 8087:8087 \
  -e MYSQL_HOST=mysql \
  -e KAFKA_BOOTSTRAP_SERVERS=kafka:9092 \
  nta-task-service:3.0.0
```

### 3. Kubernetes 部署

使用 Helm Chart 部署：
```bash
helm install nta-task-service ./deployment/helm \
  --set services.task-service.enabled=true
```

## 监控和日志

### 健康检查端点
- `/actuator/health`: 应用健康状态
- `/actuator/metrics`: 应用指标
- `/actuator/info`: 应用信息

### 日志配置
- 应用日志级别: DEBUG
- Kafka 日志级别: INFO
- 日志格式: 包含时间戳、线程、级别、类名和消息

## 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 测试覆盖率
```bash
mvn jacoco:report
```

## 故障排查

### 常见问题

1. **服务启动失败**
   - 检查 systemctl 命令是否可用
   - 确认服务文件路径权限
   - 查看应用日志中的错误信息

2. **Kafka 消息处理失败**
   - 检查 Kafka 连接配置
   - 确认主题是否存在
   - 查看消费者组状态

3. **数据库连接问题**
   - 检查数据库连接配置
   - 确认数据表是否已创建
   - 查看 MyBatis 日志

### 日志查看
```bash
# 查看应用日志
kubectl logs -f deployment/task-service

# 查看特定服务日志
systemctl status offline_thd.11.service
journalctl -u offline_thd.11.service -f
```

## 性能优化

### 建议配置
- **并发处理**: 根据硬件资源调整服务ID数量
- **Kafka 配置**: 调整批处理大小和超时时间
- **数据库连接池**: 根据并发需求配置连接池大小
- **JVM 参数**: 根据内存使用情况调整堆大小

### 监控指标
- 任务处理时间
- 服务利用率
- Kafka 消息延迟
- 数据库连接池状态

## 版本历史

- **3.0.0**: 从 Python 版本迁移到 Java，采用 DDD 架构
- **2.0.x**: Python 版本 (pcap_import_manager.py)

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

Copyright © 2024 GeekSec. All rights reserved.
