# 多阶段构建
FROM maven:3.8.6-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制 pom.xml 和源代码
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行时镜像
FROM openjdk:17-jdk-slim

# 安装必要的系统工具
RUN apt-get update && apt-get install -y \
    systemctl \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r nta && useradd -r -g nta nta

# 设置工作目录
WORKDIR /app

# 从构建阶段复制 JAR 文件
COPY --from=builder /app/target/*.jar app.jar

# 创建必要的目录
RUN mkdir -p /conf /opt/GeekSec/th/bin/conf_pub/env /opt/GeekSec/th/bin/service /etc/systemd/system /opt/GeekSec/task/thd_offline_conf

# 创建日志目录
RUN mkdir -p /app/logs

# 设置权限
RUN chown -R nta:nta /app /conf /opt/GeekSec /tmp

# 切换到应用用户
USER nta

# 暴露端口
EXPOSE 8087

# JVM 参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8087/api/task/pcap-import/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
