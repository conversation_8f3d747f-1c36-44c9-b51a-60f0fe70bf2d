server:
  port: 8087
  servlet:
    context-path: /api/task

spring:
  application:
    name: task-service
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    consumer:
      group-id: pcap-import-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 10
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000
    producer:
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.geeksec.nta.task.infrastructure.po
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

# PCAP导入配置
pcap:
  import:
    service-ids: 11,12,13
    base-conf-path: /conf
    thd-env-path: /opt/GeekSec/th/bin/conf_pub/env
    service-path: /opt/GeekSec/th/bin/service
    systemd-path: /etc/systemd/system
    thd-root: /opt/GeekSec/th
    source-conf-path: /opt/GeekSec/task/thd_offline_conf

# Kafka主题配置
kafka:
  topics:
    offline-thd: offline_thd
  consumer:
    group-id: pcap-import-group

# 日志配置
logging:
  level:
    com.geeksec.nta.task: DEBUG
    org.springframework.kafka: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
