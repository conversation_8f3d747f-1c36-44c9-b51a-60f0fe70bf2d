-- PCAP导入任务表
CREATE TABLE IF NOT EXISTS `tb_pcap_import_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `batch_id` int(11) NOT NULL COMMENT '批次ID',
  `input_dirs` text NOT NULL COMMENT '输入目录列表（逗号分隔）',
  `pcap_files` longtext COMMENT 'PCAP文件列表（逗号分隔）',
  `pcap_file_count` int(11) DEFAULT '0' COMMENT 'PCAP文件数量',
  `assigned_service_id` int(11) COMMENT '分配的服务ID',
  `assigned_service_name` varchar(100) COMMENT '分配的服务名称',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `start_time` datetime COMMENT '开始处理时间',
  `finish_time` datetime COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_batch` (`task_id`, `batch_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_service_id` (`assigned_service_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PCAP导入任务表';

-- 批次离线THD关联表（如果不存在）
CREATE TABLE IF NOT EXISTS `tb_batch_offline_thd` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `batch_id` int(11) NOT NULL COMMENT '批次ID',
  `service_id` int(11) NOT NULL COMMENT '服务ID',
  `service_name` varchar(100) NOT NULL COMMENT '服务名称',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_batch` (`task_id`, `batch_id`),
  KEY `idx_service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批次离线THD关联表';

-- 插入测试数据（可选）
-- INSERT INTO `tb_pcap_import_task` (`task_id`, `batch_id`, `input_dirs`, `status`) 
-- VALUES (1001, 2001, '/data/pcap/test1,/data/pcap/test2', 'PENDING');
