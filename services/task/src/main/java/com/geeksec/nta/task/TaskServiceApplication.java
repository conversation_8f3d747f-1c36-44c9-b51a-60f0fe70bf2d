package com.geeksec.nta.task;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 任务管理服务启动类
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootApplication
@EnableKafka
@EnableAsync
@EnableScheduling
public class TaskServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(TaskServiceApplication.class, args);
    }
}
