package com.geeksec.nta.task.domain.enums;

/**
 * 服务状态枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public enum ServiceStatus {
    /**
     * 服务运行中
     */
    ACTIVE(1, "active"),
    
    /**
     * 服务未运行
     */
    INACTIVE(2, "inactive"),
    
    /**
     * 服务不存在
     */
    NOT_FOUND(3, "not_found"),
    
    /**
     * 服务状态异常
     */
    ERROR(4, "error");
    
    private final int code;
    private final String description;
    
    ServiceStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     */
    public static ServiceStatus fromCode(int code) {
        for (ServiceStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的服务状态代码: " + code);
    }
}
