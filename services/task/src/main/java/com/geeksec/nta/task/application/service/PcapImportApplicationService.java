package com.geeksec.nta.task.application.service;

import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import com.geeksec.nta.task.domain.entity.ServiceInfo;
import com.geeksec.nta.task.domain.service.ServiceManagementDomainService;
import com.geeksec.nta.task.domain.service.RuleSyncDomainService;
import com.geeksec.nta.task.domain.repository.PcapImportTaskRepository;
import com.geeksec.nta.task.infrastructure.util.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;

/**
 * PCAP导入应用服务
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PcapImportApplicationService {
    
    private final ServiceManagementDomainService serviceManagementDomainService;
    private final RuleSyncDomainService ruleSyncDomainService;
    private final PcapImportTaskRepository pcapImportTaskRepository;
    private final FileUtil fileUtil;
    
    @Value("${pcap.import.service-ids:11,12,13}")
    private List<Integer> serviceIdList;
    
    /**
     * 处理PCAP导入请求
     * 
     * @param request 导入请求
     */
    @Transactional
    public void processImportRequest(PcapImportRequestDto request) {
        log.info("开始处理PCAP导入请求: taskId={}, batchId={}", request.getTaskId(), request.getBatchId());
        
        try {
            // 1. 创建导入任务
            PcapImportTask task = new PcapImportTask(
                request.getTaskId(), 
                request.getBatchId(), 
                request.getInputDirs()
            );
            
            // 2. 查找PCAP文件
            List<String> pcapFiles = fileUtil.findPcapFiles(request.getInputDirs());
            task.setPcapFileInfo(pcapFiles);
            
            // 3. 更新批次PCAP文件数量
            pcapImportTaskRepository.updatePcapNum(request.getBatchId(), pcapFiles.size());
            
            // 4. 查找空闲服务
            Optional<Integer> idleServiceId = serviceManagementDomainService.findIdleService(serviceIdList);
            if (idleServiceId.isEmpty()) {
                throw new RuntimeException("没有可用的空闲服务");
            }
            
            Integer serviceId = idleServiceId.get();
            String serviceName = ServiceInfo.generateServiceName(serviceId);
            task.assignService(serviceId, serviceName);
            
            log.info("找到空闲服务: serviceId={}, serviceName={}", serviceId, serviceName);
            
            // 5. 保存任务
            pcapImportTaskRepository.save(task);
            
            // 6. 准备配置路径
            Path configPath = Paths.get(String.format("/conf/%d/%s", serviceId, serviceName));
            
            // 7. 写入任务信息
            fileUtil.writeTaskInfo(configPath, task);
            
            // 8. 写入文件索引
            fileUtil.writeFileIndex(configPath, pcapFiles);
            
            // 9. 同步配置和规则
            ruleSyncDomainService.syncFilters(request.getTaskId(), configPath);
            ruleSyncDomainService.syncRules(request.getTaskId(), request.getBatchId(), configPath);
            ruleSyncDomainService.syncFullflowAndFlowlog(request.getBatchId(), configPath);
            
            // 10. 记录任务批次和探针服务关联信息
            pcapImportTaskRepository.insertBatchOfflineThd(
                request.getTaskId(), 
                request.getBatchId(), 
                serviceId, 
                serviceName
            );
            
            // 11. 启动服务
            task.startProcessing();
            pcapImportTaskRepository.save(task);
            
            boolean started = serviceManagementDomainService.restartService(serviceName);
            if (!started) {
                task.failProcessing("服务启动失败");
                pcapImportTaskRepository.save(task);
                throw new RuntimeException("服务启动失败: " + serviceName);
            }
            
            log.info("PCAP导入任务处理完成: taskId={}, batchId={}, serviceId={}", 
                request.getTaskId(), request.getBatchId(), serviceId);
                
        } catch (Exception e) {
            log.error("处理PCAP导入请求失败: taskId={}, batchId={}", 
                request.getTaskId(), request.getBatchId(), e);
            throw new RuntimeException("处理PCAP导入请求失败", e);
        }
    }
    
    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 任务信息
     */
    public Optional<PcapImportTask> getTaskStatus(Integer taskId, Integer batchId) {
        return pcapImportTaskRepository.findByTaskIdAndBatchId(taskId, batchId);
    }
    
    /**
     * 获取所有待处理任务
     * 
     * @return 待处理任务列表
     */
    public List<PcapImportTask> getPendingTasks() {
        return pcapImportTaskRepository.findPendingTasks();
    }
}
