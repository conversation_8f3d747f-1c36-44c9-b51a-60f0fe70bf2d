package com.geeksec.nta.task.infrastructure.repository;

import com.geeksec.nta.task.domain.entity.PcapImportTask;
import com.geeksec.nta.task.domain.repository.PcapImportTaskRepository;
import com.geeksec.nta.task.infrastructure.mapper.PcapImportTaskMapper;
import com.geeksec.nta.task.infrastructure.po.PcapImportTaskPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * PCAP导入任务仓储实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PcapImportTaskRepositoryImpl implements PcapImportTaskRepository {
    
    private final PcapImportTaskMapper pcapImportTaskMapper;
    
    @Override
    public PcapImportTask save(PcapImportTask task) {
        PcapImportTaskPO po = convertToPO(task);
        
        if (po.getId() == null) {
            pcapImportTaskMapper.insert(po);
        } else {
            pcapImportTaskMapper.updateById(po);
        }
        
        return convertToEntity(po);
    }
    
    @Override
    public Optional<PcapImportTask> findByTaskIdAndBatchId(Integer taskId, Integer batchId) {
        PcapImportTaskPO po = pcapImportTaskMapper.findByTaskIdAndBatchId(taskId, batchId);
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public List<PcapImportTask> findByTaskId(Integer taskId) {
        List<PcapImportTaskPO> poList = pcapImportTaskMapper.findByTaskId(taskId);
        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PcapImportTask> findProcessingTasksByServiceId(Integer serviceId) {
        List<PcapImportTaskPO> poList = pcapImportTaskMapper.findProcessingTasksByServiceId(serviceId);
        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PcapImportTask> findPendingTasks() {
        List<PcapImportTaskPO> poList = pcapImportTaskMapper.findPendingTasks();
        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public void updateStatus(Integer taskId, Integer batchId, String status) {
        pcapImportTaskMapper.updateStatus(taskId, batchId, status);
    }
    
    @Override
    public void updatePcapNum(Integer batchId, Integer pcapNum) {
        pcapImportTaskMapper.updatePcapNum(batchId, pcapNum);
    }
    
    @Override
    public void insertBatchOfflineThd(Integer taskId, Integer batchId, Integer serviceId, String serviceName) {
        pcapImportTaskMapper.insertBatchOfflineThd(taskId, batchId, serviceId, serviceName);
    }
    
    @Override
    public void delete(Integer taskId, Integer batchId) {
        pcapImportTaskMapper.deleteByTaskIdAndBatchId(taskId, batchId);
    }
    
    /**
     * 实体转PO
     */
    private PcapImportTaskPO convertToPO(PcapImportTask task) {
        PcapImportTaskPO po = new PcapImportTaskPO();
        po.setTaskId(task.getTaskId());
        po.setBatchId(task.getBatchId());
        po.setInputDirs(String.join(",", task.getInputDirs()));
        po.setPcapFiles(task.getPcapFiles() != null ? String.join(",", task.getPcapFiles()) : null);
        po.setPcapFileCount(task.getPcapFileCount());
        po.setAssignedServiceId(task.getAssignedServiceId());
        po.setAssignedServiceName(task.getAssignedServiceName());
        po.setStatus(task.getStatus());
        po.setCreatedTime(task.getCreatedTime());
        po.setStartTime(task.getStartTime());
        po.setFinishTime(task.getFinishTime());
        po.setErrorMessage(task.getErrorMessage());
        return po;
    }
    
    /**
     * PO转实体
     */
    private PcapImportTask convertToEntity(PcapImportTaskPO po) {
        PcapImportTask task = new PcapImportTask();
        task.setTaskId(po.getTaskId());
        task.setBatchId(po.getBatchId());
        task.setInputDirs(List.of(po.getInputDirs().split(",")));
        task.setPcapFiles(po.getPcapFiles() != null ? List.of(po.getPcapFiles().split(",")) : null);
        task.setPcapFileCount(po.getPcapFileCount());
        task.setAssignedServiceId(po.getAssignedServiceId());
        task.setAssignedServiceName(po.getAssignedServiceName());
        task.setStatus(po.getStatus());
        task.setCreatedTime(po.getCreatedTime());
        task.setStartTime(po.getStartTime());
        task.setFinishTime(po.getFinishTime());
        task.setErrorMessage(po.getErrorMessage());
        return task;
    }
}
