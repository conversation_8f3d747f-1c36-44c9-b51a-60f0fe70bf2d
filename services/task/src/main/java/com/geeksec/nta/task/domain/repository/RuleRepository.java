package com.geeksec.nta.task.domain.repository;

import com.geeksec.nta.task.domain.valueobject.RuleInfo;
import com.geeksec.nta.task.domain.valueobject.FilterInfo;

import java.util.List;
import java.util.Optional;

/**
 * 规则仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface RuleRepository {
    
    /**
     * 根据任务ID获取有效规则
     * 
     * @param taskId 任务ID
     * @return 规则信息列表
     */
    List<RuleInfo> findActiveRulesByTaskId(Integer taskId);
    
    /**
     * 根据规则ID获取规则信息
     * 
     * @param ruleId 规则ID
     * @return 规则信息
     */
    Optional<RuleInfo> findByRuleId(Integer ruleId);
    
    /**
     * 根据任务ID获取过滤器配置
     * 
     * @param taskId 任务ID
     * @return 过滤器信息列表
     */
    List<FilterInfo> findFilterConfigByTaskId(Integer taskId);
    
    /**
     * 根据任务ID获取过滤器状态
     * 
     * @param taskId 任务ID
     * @return 过滤器状态
     */
    Optional<Integer> findFilterStateByTaskId(Integer taskId);
    
    /**
     * 根据批次ID获取全流量状态
     * 
     * @param batchId 批次ID
     * @return 全流量状态
     */
    Optional<String> findFullflowStateByBatchId(Integer batchId);
    
    /**
     * 根据批次ID获取流日志状态
     * 
     * @param batchId 批次ID
     * @return 流日志状态
     */
    Optional<String> findFlowlogStateByBatchId(Integer batchId);
    
    /**
     * 根据批次ID获取全流量和流日志状态
     * 
     * @param batchId 批次ID
     * @return 包含全流量和流日志状态的映射
     */
    Optional<BatchFlowConfig> findFlowConfigByBatchId(Integer batchId);
    
    /**
     * 批次流配置
     */
    class BatchFlowConfig {
        private String fullflowState;
        private String flowlogState;
        
        public BatchFlowConfig(String fullflowState, String flowlogState) {
            this.fullflowState = fullflowState;
            this.flowlogState = flowlogState;
        }
        
        public String getFullflowState() {
            return fullflowState;
        }
        
        public String getFlowlogState() {
            return flowlogState;
        }
    }
}
