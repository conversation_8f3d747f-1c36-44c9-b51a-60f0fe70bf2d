package com.geeksec.nta.task.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * PCAP导入请求DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PcapImportRequestDto {
    
    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
    
    /**
     * 批次ID
     */
    @NotNull(message = "批次ID不能为空")
    private Integer batchId;
    
    /**
     * 输入目录列表
     */
    @NotEmpty(message = "输入目录列表不能为空")
    private List<String> inputDirs;
}
