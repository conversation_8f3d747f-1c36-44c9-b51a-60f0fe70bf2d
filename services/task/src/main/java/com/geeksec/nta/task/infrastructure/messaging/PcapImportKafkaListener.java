package com.geeksec.nta.task.infrastructure.messaging;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.application.service.PcapImportApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * PCAP导入Kafka消息监听器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PcapImportKafkaListener {
    
    private final PcapImportApplicationService pcapImportApplicationService;
    private final ObjectMapper objectMapper;
    
    /**
     * 监听offline_thd主题的消息
     * 
     * @param message 消息内容
     * @param partition 分区
     * @param offset 偏移量
     * @param acknowledgment 确认对象
     */
    @KafkaListener(
        topics = "${kafka.topics.offline-thd:offline_thd}",
        groupId = "${kafka.consumer.group-id:pcap-import-group}",
        containerFactory = "kafkaListenerContainerFactory"
    )
    public void handlePcapImportMessage(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {
        
        log.info("接收到PCAP导入消息: partition={}, offset={}, message={}", partition, offset, message);
        
        try {
            // 解析消息
            PcapImportRequestDto request = parseMessage(message);
            
            // 处理导入请求
            pcapImportApplicationService.processImportRequest(request);
            
            // 手动确认消息
            acknowledgment.acknowledge();
            
            log.info("PCAP导入消息处理成功: taskId={}, batchId={}", 
                request.getTaskId(), request.getBatchId());
                
        } catch (Exception e) {
            log.error("处理PCAP导入消息失败: partition={}, offset={}, message={}", 
                partition, offset, message, e);
            
            // 根据业务需求决定是否确认消息
            // 这里选择确认消息以避免重复处理，但可以根据实际情况调整
            acknowledgment.acknowledge();
        }
    }
    
    /**
     * 解析Kafka消息
     * 
     * @param message 消息内容
     * @return 解析后的请求对象
     */
    private PcapImportRequestDto parseMessage(String message) {
        try {
            return objectMapper.readValue(message, PcapImportRequestDto.class);
        } catch (Exception e) {
            log.error("解析Kafka消息失败: message={}", message, e);
            throw new RuntimeException("解析Kafka消息失败", e);
        }
    }
}
