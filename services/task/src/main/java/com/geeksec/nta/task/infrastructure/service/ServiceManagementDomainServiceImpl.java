package com.geeksec.nta.task.infrastructure.service;

import com.geeksec.nta.task.domain.entity.ServiceInfo;
import com.geeksec.nta.task.domain.enums.ServiceStatus;
import com.geeksec.nta.task.domain.service.ServiceManagementDomainService;
import com.geeksec.nta.task.infrastructure.util.SystemCommandUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 服务管理领域服务实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceManagementDomainServiceImpl implements ServiceManagementDomainService {
    
    private final SystemCommandUtil systemCommandUtil;
    
    @Value("${pcap.import.base-conf-path:/conf}")
    private String baseConfPath;
    
    @Value("${pcap.import.thd-env-path:/opt/GeekSec/th/bin/conf_pub/env}")
    private String thdEnvPath;
    
    @Value("${pcap.import.service-path:/opt/GeekSec/th/bin/service}")
    private String servicePath;
    
    @Value("${pcap.import.systemd-path:/etc/systemd/system}")
    private String systemdPath;
    
    @Value("${pcap.import.thd-root:/opt/GeekSec/th}")
    private String thdRoot;
    
    @Value("${pcap.import.source-conf-path:/opt/GeekSec/task/thd_offline_conf}")
    private String sourceConfPath;
    
    @Override
    public ServiceStatus checkServiceStatus(String serviceName) {
        try {
            // 检查服务是否存在
            String listResult = systemCommandUtil.executeCommand("systemctl", "list-unit-files", "--no-pager");
            
            if (!listResult.contains(serviceName)) {
                return ServiceStatus.NOT_FOUND;
            }
            
            // 检查服务是否活跃
            String activeResult = systemCommandUtil.executeCommand("systemctl", "is-active", serviceName);
            
            if ("active".equals(activeResult.trim())) {
                return ServiceStatus.ACTIVE;
            } else {
                return ServiceStatus.INACTIVE;
            }
            
        } catch (Exception e) {
            log.error("检查服务状态失败: serviceName={}", serviceName, e);
            return ServiceStatus.ERROR;
        }
    }
    
    @Override
    public ServiceInfo createService(Integer serviceId) {
        String serviceName = ServiceInfo.generateServiceName(serviceId);
        ServiceStatus status = checkServiceStatus(serviceName);
        
        if (status != ServiceStatus.NOT_FOUND) {
            log.info("服务已存在: serviceId={}, serviceName={}, status={}", serviceId, serviceName, status);
            return buildServiceInfo(serviceId, serviceName, status);
        }
        
        try {
            // 创建配置目录
            Path confDir = Paths.get(baseConfPath, serviceId.toString(), serviceName);
            Files.createDirectories(confDir);
            
            // 复制配置文件
            copyConfigFiles(Paths.get(sourceConfPath), confDir);
            
            // 创建环境文件
            createEnvironmentFile(serviceId, confDir);
            
            // 创建服务文件
            createServiceFile(serviceId, serviceName);
            
            // 创建符号链接
            createSymbolicLink(serviceName);
            
            log.info("服务创建成功: serviceId={}, serviceName={}", serviceId, serviceName);
            return buildServiceInfo(serviceId, serviceName, ServiceStatus.INACTIVE);
            
        } catch (Exception e) {
            log.error("创建服务失败: serviceId={}, serviceName={}", serviceId, serviceName, e);
            throw new RuntimeException("创建服务失败", e);
        }
    }
    
    @Override
    public Optional<Integer> findIdleService(List<Integer> serviceIdList) {
        for (Integer serviceId : serviceIdList) {
            String serviceName = ServiceInfo.generateServiceName(serviceId);
            ServiceStatus status = checkServiceStatus(serviceName);
            
            // 如果服务未创建，创建后返回
            if (status == ServiceStatus.NOT_FOUND) {
                log.info("服务未找到，正在创建: serviceId={}, serviceName={}", serviceId, serviceName);
                createService(serviceId);
                return Optional.of(serviceId);
            }
            
            // 如果服务空闲，直接返回
            if (status == ServiceStatus.INACTIVE) {
                log.info("找到空闲服务: serviceId={}, serviceName={}", serviceId, serviceName);
                return Optional.of(serviceId);
            }
            
            if (status == ServiceStatus.ERROR) {
                log.warn("服务状态异常: serviceId={}, serviceName={}", serviceId, serviceName);
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        
        // 如果没有找到空闲服务，等待并重试
        log.warn("没有找到空闲服务，等待重试...");
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return findIdleService(serviceIdList);
    }
    
    @Override
    public boolean startService(String serviceName) {
        try {
            systemCommandUtil.executeCommand("systemctl", "start", serviceName);
            
            // 等待一段时间后检查状态
            Thread.sleep(2000);
            ServiceStatus status = checkServiceStatus(serviceName);
            
            boolean success = status == ServiceStatus.ACTIVE;
            log.info("启动服务: serviceName={}, success={}", serviceName, success);
            return success;
            
        } catch (Exception e) {
            log.error("启动服务失败: serviceName={}", serviceName, e);
            return false;
        }
    }
    
    @Override
    public boolean stopService(String serviceName) {
        try {
            systemCommandUtil.executeCommand("systemctl", "stop", serviceName);
            
            // 等待一段时间后检查状态
            Thread.sleep(2000);
            ServiceStatus status = checkServiceStatus(serviceName);
            
            boolean success = status == ServiceStatus.INACTIVE;
            log.info("停止服务: serviceName={}, success={}", serviceName, success);
            return success;
            
        } catch (Exception e) {
            log.error("停止服务失败: serviceName={}", serviceName, e);
            return false;
        }
    }
    
    @Override
    public boolean restartService(String serviceName) {
        try {
            systemCommandUtil.executeCommand("systemctl", "restart", serviceName);
            
            // 等待一段时间后检查状态
            Thread.sleep(3000);
            ServiceStatus status = checkServiceStatus(serviceName);
            
            boolean success = status == ServiceStatus.ACTIVE;
            log.info("重启服务: serviceName={}, success={}", serviceName, success);
            return success;
            
        } catch (Exception e) {
            log.error("重启服务失败: serviceName={}", serviceName, e);
            return false;
        }
    }
    
    @Override
    public Optional<ServiceInfo> getServiceInfo(Integer serviceId) {
        String serviceName = ServiceInfo.generateServiceName(serviceId);
        ServiceStatus status = checkServiceStatus(serviceName);
        
        if (status == ServiceStatus.NOT_FOUND) {
            return Optional.empty();
        }
        
        return Optional.of(buildServiceInfo(serviceId, serviceName, status));
    }
    
    @Override
    public List<ServiceInfo> getAllServiceInfo(List<Integer> serviceIdList) {
        List<ServiceInfo> serviceInfoList = new ArrayList<>();
        
        for (Integer serviceId : serviceIdList) {
            getServiceInfo(serviceId).ifPresent(serviceInfoList::add);
        }
        
        return serviceInfoList;
    }
    
    /**
     * 复制配置文件
     */
    private void copyConfigFiles(Path sourceDir, Path targetDir) throws IOException {
        if (!Files.exists(sourceDir)) {
            log.warn("源配置目录不存在: {}", sourceDir);
            return;
        }
        
        Files.walk(sourceDir)
            .forEach(source -> {
                try {
                    Path target = targetDir.resolve(sourceDir.relativize(source));
                    if (Files.isDirectory(source)) {
                        Files.createDirectories(target);
                    } else {
                        Files.copy(source, target);
                    }
                } catch (IOException e) {
                    log.error("复制文件失败: source={}, target={}", source, targetDir, e);
                }
            });
    }
    
    /**
     * 创建环境文件
     */
    private void createEnvironmentFile(Integer serviceId, Path confDir) throws IOException {
        String envContent = String.format("""
            THE_ROOT=%s
            PATH=%s/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin
            LD_LIBRARY_PATH=%s/bin/:%s/sdk/lib/
            THE_CONFPUB_PATH=%s/bin/conf_pub/
            THE_DB_PATH=%s/bin/db/
            THE_CONF_PATH=%s
            THE_ID=%d
            """, thdRoot, thdRoot, thdRoot, thdRoot, thdRoot, thdRoot, confDir, serviceId);
        
        Path envFile = Paths.get(thdEnvPath, "THD_ENV_" + serviceId);
        Files.writeString(envFile, envContent.trim());
    }
    
    /**
     * 创建服务文件
     */
    private void createServiceFile(Integer serviceId, String serviceName) throws IOException {
        Path envFile = Paths.get(thdEnvPath, "THD_ENV_" + serviceId);
        
        String serviceContent = String.format("""
            [Unit]
            Description=thd service id %d
            
            [Service]
            Type=simple
            EnvironmentFile=%s
            
            ExecStart=%s/bin/thd.product_analysis
            
            [Install]
            WantedBy=multi-user.target
            """, serviceId, envFile, thdRoot);
        
        Path serviceFile = Paths.get(servicePath, serviceName);
        Files.writeString(serviceFile, serviceContent.trim());
    }
    
    /**
     * 创建符号链接
     */
    private void createSymbolicLink(String serviceName) throws IOException {
        Path serviceFile = Paths.get(servicePath, serviceName);
        Path symlinkPath = Paths.get(systemdPath, serviceName);
        
        // 如果符号链接已存在，先删除
        if (Files.exists(symlinkPath) || Files.isSymbolicLink(symlinkPath)) {
            Files.delete(symlinkPath);
        }
        
        Files.createSymbolicLink(symlinkPath, serviceFile);
    }
    
    /**
     * 构建服务信息对象
     */
    private ServiceInfo buildServiceInfo(Integer serviceId, String serviceName, ServiceStatus status) {
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId(serviceId);
        serviceInfo.setServiceName(serviceName);
        serviceInfo.setStatus(status);
        serviceInfo.setConfigPath(Paths.get(baseConfPath, serviceId.toString(), serviceName).toString());
        serviceInfo.setEnvFilePath(Paths.get(thdEnvPath, "THD_ENV_" + serviceId).toString());
        serviceInfo.setServiceFilePath(Paths.get(servicePath, serviceName).toString());
        serviceInfo.setSymlinkPath(Paths.get(systemdPath, serviceName).toString());
        serviceInfo.setUpdatedTime(LocalDateTime.now());
        return serviceInfo;
    }
}
