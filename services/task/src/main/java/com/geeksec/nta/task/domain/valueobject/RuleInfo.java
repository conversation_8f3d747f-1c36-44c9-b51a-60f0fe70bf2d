package com.geeksec.nta.task.domain.valueobject;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 规则信息值对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RuleInfo {
    
    /**
     * 规则ID
     */
    private Integer ruleId;
    
    /**
     * 规则级别
     */
    private Integer ruleLevel;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则描述
     */
    private String ruleDesc;
    
    /**
     * 规则大小
     */
    private Integer ruleSize;
    
    /**
     * 捕获模式
     */
    private String captureMode;
    
    /**
     * 规则JSON内容
     */
    private String ruleJson;
    
    /**
     * 规则状态
     */
    private String ruleState;
    
    /**
     * 规则类型
     */
    private String ruleType;
    
    /**
     * 动态库响应开关
     */
    private String libRespondOpen;
    
    /**
     * 动态库名称
     */
    private String libRespondLib;
    
    /**
     * 动态库配置
     */
    private String libRespondConfig;
    
    /**
     * 动态库会话结束
     */
    private String libRespondSessionEnd;
    
    /**
     * 动态库数据（Base64编码）
     */
    private String libDataSo;
    
    /**
     * 动态库配置数据（Base64编码）
     */
    private String libDataConf;
    
    /**
     * PB丢弃
     */
    private String pbDrop;
    
    /**
     * PCAP丢弃
     */
    private String pcapDrop;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 检查规则是否生效
     */
    public boolean isActive() {
        return "生效".equals(this.ruleState);
    }
    
    /**
     * 检查是否为动态库规则
     */
    public boolean isDynamicLibRule() {
        return ruleType != null && ruleType.contains("6");
    }
    
    /**
     * 检查是否为复杂规则
     */
    public boolean isComplexRule() {
        return ruleType != null && ruleType.contains("7");
    }
}
