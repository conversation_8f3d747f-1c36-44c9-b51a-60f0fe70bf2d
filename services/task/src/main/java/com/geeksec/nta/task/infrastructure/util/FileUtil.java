package com.geeksec.nta.task.infrastructure.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 文件处理工具类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FileUtil {
    
    private final ObjectMapper objectMapper;
    
    private static final List<String> PCAP_EXTENSIONS = List.of(".pcap", ".cap", ".pcapng");
    
    /**
     * 查找PCAP文件
     * 
     * @param inputDirs 输入目录列表
     * @return PCAP文件路径列表
     */
    public List<String> findPcapFiles(List<String> inputDirs) {
        List<String> pcapFiles = new ArrayList<>();
        
        for (String inputPath : inputDirs) {
            Path path = Paths.get(inputPath);
            
            if (Files.isRegularFile(path)) {
                // 如果是文件，检查是否为PCAP文件
                if (isPcapFile(path)) {
                    pcapFiles.add(inputPath);
                }
            } else if (Files.isDirectory(path)) {
                // 如果是目录，递归查找PCAP文件
                pcapFiles.addAll(findPcapFilesInDirectory(path));
            } else {
                log.warn("路径不存在或不是文件/目录: {}", inputPath);
            }
        }
        
        log.info("找到PCAP文件数量: {}", pcapFiles.size());
        return pcapFiles;
    }
    
    /**
     * 在目录中递归查找PCAP文件
     * 
     * @param directory 目录路径
     * @return PCAP文件路径列表
     */
    private List<String> findPcapFilesInDirectory(Path directory) {
        List<String> pcapFiles = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.filter(Files::isRegularFile)
                 .filter(this::isPcapFile)
                 .forEach(path -> pcapFiles.add(path.toString()));
        } catch (IOException e) {
            log.error("遍历目录失败: {}", directory, e);
        }
        
        return pcapFiles;
    }
    
    /**
     * 检查文件是否为PCAP文件
     * 
     * @param file 文件路径
     * @return 是否为PCAP文件
     */
    private boolean isPcapFile(Path file) {
        String fileName = file.getFileName().toString().toLowerCase();
        return PCAP_EXTENSIONS.stream().anyMatch(fileName::endsWith);
    }
    
    /**
     * 写入任务信息文件
     * 
     * @param configPath 配置路径
     * @param task 任务信息
     */
    public void writeTaskInfo(Path configPath, PcapImportTask task) {
        try {
            Files.createDirectories(configPath);
            
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("task_id", task.getTaskId());
            taskInfo.put("batch_id", task.getBatchId());
            
            Path taskInfoFile = configPath.resolve("task_info.json");
            String taskInfoJson = objectMapper.writeValueAsString(taskInfo);
            Files.writeString(taskInfoFile, taskInfoJson);
            
            log.debug("写入任务信息文件: {}", taskInfoFile);
            
        } catch (IOException e) {
            log.error("写入任务信息文件失败: configPath={}", configPath, e);
            throw new RuntimeException("写入任务信息文件失败", e);
        }
    }
    
    /**
     * 写入文件索引
     * 
     * @param configPath 配置路径
     * @param pcapFiles PCAP文件列表
     */
    public void writeFileIndex(Path configPath, List<String> pcapFiles) {
        try {
            // 删除已存在的文件索引
            Path fileIndexPath = configPath.resolve("file_index.txt");
            Files.deleteIfExists(fileIndexPath);
            
            // 写入PCAP文件列表
            StringBuilder content = new StringBuilder();
            for (String pcapFile : pcapFiles) {
                content.append(pcapFile).append("\n");
            }
            
            // 添加结束标记文件
            content.append("end.pcap\n");
            
            Files.writeString(fileIndexPath, content.toString());
            
            // 清空任务PCAP读取配置文件
            Path taskPcapReadConf = configPath.resolve("task_pcap_read.conf");
            Files.writeString(taskPcapReadConf, "");
            
            log.debug("写入文件索引: {}, 文件数量: {}", fileIndexPath, pcapFiles.size());
            
        } catch (IOException e) {
            log.error("写入文件索引失败: configPath={}", configPath, e);
            throw new RuntimeException("写入文件索引失败", e);
        }
    }
    
    /**
     * 写入文本文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     */
    public void writeFile(Path filePath, String content) {
        try {
            Files.createDirectories(filePath.getParent());
            Files.writeString(filePath, content);
            log.debug("写入文件: {}", filePath);
        } catch (IOException e) {
            log.error("写入文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入文件失败", e);
        }
    }
    
    /**
     * 写入Base64编码的文件
     * 
     * @param filePath 文件路径
     * @param base64Content Base64编码的内容
     */
    public void writeBase64File(Path filePath, String base64Content) {
        try {
            Files.createDirectories(filePath.getParent());
            byte[] decodedBytes = Base64.getDecoder().decode(base64Content);
            Files.write(filePath, decodedBytes);
            log.debug("写入Base64文件: {}", filePath);
        } catch (Exception e) {
            log.error("写入Base64文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入Base64文件失败", e);
        }
    }
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容
     */
    public String readFile(Path filePath) {
        try {
            return Files.readString(filePath);
        } catch (IOException e) {
            log.error("读取文件失败: filePath={}", filePath, e);
            throw new RuntimeException("读取文件失败", e);
        }
    }
    
    /**
     * 复制文件
     * 
     * @param source 源文件路径
     * @param target 目标文件路径
     */
    public void copyFile(Path source, Path target) {
        try {
            Files.createDirectories(target.getParent());
            Files.copy(source, target);
            log.debug("复制文件: {} -> {}", source, target);
        } catch (IOException e) {
            log.error("复制文件失败: source={}, target={}", source, target, e);
            throw new RuntimeException("复制文件失败", e);
        }
    }
    
    /**
     * 创建目录
     * 
     * @param directory 目录路径
     */
    public void createDirectories(Path directory) {
        try {
            Files.createDirectories(directory);
            log.debug("创建目录: {}", directory);
        } catch (IOException e) {
            log.error("创建目录失败: directory={}", directory, e);
            throw new RuntimeException("创建目录失败", e);
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public boolean exists(Path filePath) {
        return Files.exists(filePath);
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     */
    public void deleteFile(Path filePath) {
        try {
            Files.deleteIfExists(filePath);
            log.debug("删除文件: {}", filePath);
        } catch (IOException e) {
            log.error("删除文件失败: filePath={}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public long getFileSize(Path filePath) {
        try {
            return Files.size(filePath);
        } catch (IOException e) {
            log.error("获取文件大小失败: filePath={}", filePath, e);
            return 0;
        }
    }
}
