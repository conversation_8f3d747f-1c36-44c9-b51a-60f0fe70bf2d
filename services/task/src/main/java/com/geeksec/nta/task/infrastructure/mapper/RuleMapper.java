package com.geeksec.nta.task.infrastructure.mapper;

import com.geeksec.nta.task.domain.repository.RuleRepository.BatchFlowConfig;
import com.geeksec.nta.task.domain.valueobject.FilterInfo;
import com.geeksec.nta.task.domain.valueobject.RuleInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 规则Mapper接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface RuleMapper {
    
    /**
     * 根据任务ID获取有效规则
     */
    @Select("SELECT rule_id, rule_level, rule_name, rule_desc, rule_size, capture_mode, " +
            "rule_json, created_time, pb_drop, pcap_drop, updated_time, rule_state, " +
            "lib_respond_open, lib_respond_lib, lib_respond_config, lib_respond_session_end, " +
            "lib_data_so, lib_data_conf, rule_type " +
            "FROM tb_rule " +
            "WHERE rule_state = '生效' AND task_id = #{taskId}")
    List<RuleInfo> findActiveRulesByTaskId(@Param("taskId") Integer taskId);
    
    /**
     * 根据规则ID获取规则信息
     */
    @Select("SELECT rule_id, rule_level, rule_name, rule_desc, rule_size, capture_mode, " +
            "rule_json, created_time, pb_drop, pcap_drop, updated_time, rule_state, " +
            "lib_respond_open, lib_respond_lib, lib_respond_config, lib_respond_session_end, " +
            "lib_data_so, lib_data_conf, rule_type " +
            "FROM tb_rule WHERE rule_id = #{ruleId}")
    RuleInfo findByRuleId(@Param("ruleId") Integer ruleId);
    
    /**
     * 根据任务ID获取过滤器配置
     */
    @Select("SELECT b.id, b.ip, a.state, b.filter_json " +
            "FROM tb_filter_state a, tb_filter_config b " +
            "WHERE b.status = 1 AND a.task_id = b.task_id AND b.task_id = #{taskId}")
    List<FilterInfo> findFilterConfigByTaskId(@Param("taskId") Integer taskId);
    
    /**
     * 根据任务ID获取过滤器状态
     */
    @Select("SELECT state FROM tb_filter_state WHERE task_id = #{taskId}")
    Integer findFilterStateByTaskId(@Param("taskId") Integer taskId);
    
    /**
     * 根据批次ID获取全流量状态
     */
    @Select("SELECT fullflow_state FROM th_analysis.tb_task_batch WHERE batch_id = #{batchId}")
    String findFullflowStateByBatchId(@Param("batchId") Integer batchId);
    
    /**
     * 根据批次ID获取流日志状态
     */
    @Select("SELECT flowlog_state FROM th_analysis.tb_task_batch WHERE batch_id = #{batchId}")
    String findFlowlogStateByBatchId(@Param("batchId") Integer batchId);
    
    /**
     * 根据批次ID获取全流量和流日志状态
     */
    @Select("SELECT fullflow_state, flowlog_state FROM th_analysis.tb_task_batch WHERE batch_id = #{batchId}")
    BatchFlowConfig findFlowConfigByBatchId(@Param("batchId") Integer batchId);
}
