package com.geeksec.nta.task.domain.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP导入任务聚合根
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PcapImportTask {
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 批次ID
     */
    private Integer batchId;
    
    /**
     * 输入目录列表
     */
    private List<String> inputDirs;
    
    /**
     * PCAP文件列表
     */
    private List<String> pcapFiles;
    
    /**
     * PCAP文件数量
     */
    private Integer pcapFileCount;
    
    /**
     * 分配的服务ID
     */
    private Integer assignedServiceId;
    
    /**
     * 分配的服务名称
     */
    private String assignedServiceName;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 开始处理时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 构造函数 - 从Kafka消息创建
     */
    public PcapImportTask(Integer taskId, Integer batchId, List<String> inputDirs) {
        this.taskId = taskId;
        this.batchId = batchId;
        this.inputDirs = inputDirs;
        this.status = "PENDING";
        this.createdTime = LocalDateTime.now();
    }
    
    /**
     * 分配服务
     */
    public void assignService(Integer serviceId, String serviceName) {
        this.assignedServiceId = serviceId;
        this.assignedServiceName = serviceName;
    }
    
    /**
     * 开始处理
     */
    public void startProcessing() {
        this.status = "PROCESSING";
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 完成处理
     */
    public void completeProcessing() {
        this.status = "COMPLETED";
        this.finishTime = LocalDateTime.now();
    }
    
    /**
     * 处理失败
     */
    public void failProcessing(String errorMessage) {
        this.status = "FAILED";
        this.errorMessage = errorMessage;
        this.finishTime = LocalDateTime.now();
    }
    
    /**
     * 设置PCAP文件信息
     */
    public void setPcapFileInfo(List<String> pcapFiles) {
        this.pcapFiles = pcapFiles;
        this.pcapFileCount = pcapFiles != null ? pcapFiles.size() : 0;
    }
}
