package com.geeksec.nta.task.domain.valueobject;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 过滤器信息值对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterInfo {
    
    /**
     * 过滤器ID
     */
    private Integer id;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 过滤器状态（0-pass, 1-drop）
     */
    private Integer state;
    
    /**
     * 过滤器JSON配置
     */
    private String filterJson;
    
    /**
     * 过滤器状态
     */
    private Integer status;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 获取响应类型
     */
    public String getResponseType() {
        return state != null && state == 0 ? "pass" : "drop";
    }
    
    /**
     * 检查过滤器是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
}
