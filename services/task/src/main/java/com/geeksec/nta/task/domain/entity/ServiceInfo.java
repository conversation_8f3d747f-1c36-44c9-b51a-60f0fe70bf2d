package com.geeksec.nta.task.domain.entity;

import com.geeksec.nta.task.domain.enums.ServiceStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 服务信息实体
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceInfo {
    
    /**
     * 服务ID
     */
    private Integer serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务状态
     */
    private ServiceStatus status;
    
    /**
     * 配置路径
     */
    private String configPath;
    
    /**
     * 环境文件路径
     */
    private String envFilePath;
    
    /**
     * 服务文件路径
     */
    private String serviceFilePath;
    
    /**
     * 符号链接路径
     */
    private String symlinkPath;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 生成服务名称
     */
    public static String generateServiceName(Integer serviceId) {
        return String.format("offline_thd.%d.service", serviceId);
    }
    
    /**
     * 检查服务是否空闲
     */
    public boolean isIdle() {
        return ServiceStatus.INACTIVE.equals(this.status);
    }
    
    /**
     * 检查服务是否活跃
     */
    public boolean isActive() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 检查服务是否存在
     */
    public boolean exists() {
        return !ServiceStatus.NOT_FOUND.equals(this.status);
    }
}
