package com.geeksec.nta.task.domain.service;

import com.geeksec.nta.task.domain.valueobject.RuleInfo;
import com.geeksec.nta.task.domain.valueobject.FilterInfo;

import java.nio.file.Path;
import java.util.List;

/**
 * 规则同步领域服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface RuleSyncDomainService {
    
    /**
     * 同步规则到指定路径
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param taskPath 任务路径
     */
    void syncRules(Integer taskId, Integer batchId, Path taskPath);
    
    /**
     * 同步过滤器配置
     * 
     * @param taskId 任务ID
     * @param taskPath 任务路径
     */
    void syncFilters(Integer taskId, Path taskPath);
    
    /**
     * 同步全流量和流日志配置
     * 
     * @param batchId 批次ID
     * @param taskPath 任务路径
     */
    void syncFullflowAndFlowlog(Integer batchId, Path taskPath);
    
    /**
     * 获取任务的有效规则列表
     * 
     * @param taskId 任务ID
     * @return 规则信息列表
     */
    List<RuleInfo> getActiveRules(Integer taskId);
    
    /**
     * 获取任务的过滤器配置
     * 
     * @param taskId 任务ID
     * @return 过滤器信息列表
     */
    List<FilterInfo> getFilterConfig(Integer taskId);
    
    /**
     * 创建规则文件
     * 
     * @param rules 规则列表
     * @param ruleFilePath 规则文件路径
     * @param taskPath 任务路径
     */
    void createRuleFile(List<RuleInfo> rules, Path ruleFilePath, Path taskPath);
    
    /**
     * 处理动态库规则
     * 
     * @param rule 规则信息
     * @param taskPath 任务路径
     */
    void processDynamicLibRule(RuleInfo rule, Path taskPath);
    
    /**
     * 处理复杂规则
     * 
     * @param rule 规则信息
     * @param taskPath 任务路径
     */
    void processComplexRule(RuleInfo rule, Path taskPath);
}
