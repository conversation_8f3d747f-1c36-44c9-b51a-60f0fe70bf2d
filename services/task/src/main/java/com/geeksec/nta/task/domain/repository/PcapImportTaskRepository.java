package com.geeksec.nta.task.domain.repository;

import com.geeksec.nta.task.domain.entity.PcapImportTask;

import java.util.List;
import java.util.Optional;

/**
 * PCAP导入任务仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface PcapImportTaskRepository {
    
    /**
     * 保存任务
     * 
     * @param task 任务实体
     * @return 保存后的任务实体
     */
    PcapImportTask save(PcapImportTask task);
    
    /**
     * 根据ID查找任务
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 任务实体
     */
    Optional<PcapImportTask> findByTaskIdAndBatchId(Integer taskId, Integer batchId);
    
    /**
     * 根据任务ID查找所有批次
     * 
     * @param taskId 任务ID
     * @return 任务列表
     */
    List<PcapImportTask> findByTaskId(Integer taskId);
    
    /**
     * 根据服务ID查找正在处理的任务
     * 
     * @param serviceId 服务ID
     * @return 任务列表
     */
    List<PcapImportTask> findProcessingTasksByServiceId(Integer serviceId);
    
    /**
     * 查找所有待处理的任务
     * 
     * @return 任务列表
     */
    List<PcapImportTask> findPendingTasks();
    
    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param status 新状态
     */
    void updateStatus(Integer taskId, Integer batchId, String status);
    
    /**
     * 更新PCAP文件数量
     * 
     * @param batchId 批次ID
     * @param pcapNum PCAP文件数量
     */
    void updatePcapNum(Integer batchId, Integer pcapNum);
    
    /**
     * 插入批次离线THD记录
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param serviceId 服务ID
     * @param serviceName 服务名称
     */
    void insertBatchOfflineThd(Integer taskId, Integer batchId, Integer serviceId, String serviceName);
    
    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     */
    void delete(Integer taskId, Integer batchId);
}
