package com.geeksec.nta.task.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PCAP导入任务持久化对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@TableName("tb_pcap_import_task")
public class PcapImportTaskPO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 批次ID
     */
    private Integer batchId;
    
    /**
     * 输入目录列表（逗号分隔）
     */
    private String inputDirs;
    
    /**
     * PCAP文件列表（逗号分隔）
     */
    private String pcapFiles;
    
    /**
     * PCAP文件数量
     */
    private Integer pcapFileCount;
    
    /**
     * 分配的服务ID
     */
    private Integer assignedServiceId;
    
    /**
     * 分配的服务名称
     */
    private String assignedServiceName;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 开始处理时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}
