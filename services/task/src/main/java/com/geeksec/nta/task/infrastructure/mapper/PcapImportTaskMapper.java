package com.geeksec.nta.task.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.nta.task.infrastructure.po.PcapImportTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * PCAP导入任务Mapper接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface PcapImportTaskMapper extends BaseMapper<PcapImportTaskPO> {
    
    /**
     * 根据任务ID和批次ID查找任务
     */
    @Select("SELECT * FROM tb_pcap_import_task WHERE task_id = #{taskId} AND batch_id = #{batchId}")
    PcapImportTaskPO findByTaskIdAndBatchId(@Param("taskId") Integer taskId, @Param("batchId") Integer batchId);
    
    /**
     * 根据任务ID查找所有批次
     */
    @Select("SELECT * FROM tb_pcap_import_task WHERE task_id = #{taskId} ORDER BY created_time DESC")
    List<PcapImportTaskPO> findByTaskId(@Param("taskId") Integer taskId);
    
    /**
     * 根据服务ID查找正在处理的任务
     */
    @Select("SELECT * FROM tb_pcap_import_task WHERE assigned_service_id = #{serviceId} AND status = 'PROCESSING'")
    List<PcapImportTaskPO> findProcessingTasksByServiceId(@Param("serviceId") Integer serviceId);
    
    /**
     * 查找所有待处理的任务
     */
    @Select("SELECT * FROM tb_pcap_import_task WHERE status = 'PENDING' ORDER BY created_time ASC")
    List<PcapImportTaskPO> findPendingTasks();
    
    /**
     * 更新任务状态
     */
    @Update("UPDATE tb_pcap_import_task SET status = #{status} WHERE task_id = #{taskId} AND batch_id = #{batchId}")
    void updateStatus(@Param("taskId") Integer taskId, @Param("batchId") Integer batchId, @Param("status") String status);
    
    /**
     * 更新PCAP文件数量
     */
    @Update("UPDATE tb_task_batch SET pcap_num = #{pcapNum} WHERE batch_id = #{batchId}")
    void updatePcapNum(@Param("batchId") Integer batchId, @Param("pcapNum") Integer pcapNum);
    
    /**
     * 插入批次离线THD记录
     */
    @Insert("INSERT INTO tb_batch_offline_thd(task_id, batch_id, service_id, service_name) " +
            "VALUES(#{taskId}, #{batchId}, #{serviceId}, #{serviceName})")
    void insertBatchOfflineThd(@Param("taskId") Integer taskId, 
                              @Param("batchId") Integer batchId, 
                              @Param("serviceId") Integer serviceId, 
                              @Param("serviceName") String serviceName);
    
    /**
     * 根据任务ID和批次ID删除任务
     */
    @Delete("DELETE FROM tb_pcap_import_task WHERE task_id = #{taskId} AND batch_id = #{batchId}")
    void deleteByTaskIdAndBatchId(@Param("taskId") Integer taskId, @Param("batchId") Integer batchId);
}
