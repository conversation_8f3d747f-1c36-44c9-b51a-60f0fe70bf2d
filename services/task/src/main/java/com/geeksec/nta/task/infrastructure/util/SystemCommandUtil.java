package com.geeksec.nta.task.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

/**
 * 系统命令执行工具类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
@Slf4j
public class SystemCommandUtil {
    
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;
    
    /**
     * 执行系统命令
     * 
     * @param command 命令及参数
     * @return 命令输出结果
     * @throws IOException 执行异常
     * @throws InterruptedException 中断异常
     */
    public String executeCommand(String... command) throws IOException, InterruptedException {
        return executeCommand(DEFAULT_TIMEOUT_SECONDS, command);
    }
    
    /**
     * 执行系统命令（带超时）
     * 
     * @param timeoutSeconds 超时时间（秒）
     * @param command 命令及参数
     * @return 命令输出结果
     * @throws IOException 执行异常
     * @throws InterruptedException 中断异常
     */
    public String executeCommand(int timeoutSeconds, String... command) throws IOException, InterruptedException {
        log.debug("执行系统命令: {}", String.join(" ", command));
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();
        
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        
        boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("命令执行超时: " + String.join(" ", command));
        }
        
        int exitCode = process.exitValue();
        String result = output.toString().trim();
        
        log.debug("命令执行完成: exitCode={}, output={}", exitCode, result);
        
        if (exitCode != 0) {
            log.warn("命令执行返回非零退出码: exitCode={}, command={}, output={}", 
                exitCode, String.join(" ", command), result);
        }
        
        return result;
    }
    
    /**
     * 执行系统命令（忽略错误）
     * 
     * @param command 命令及参数
     * @return 命令输出结果，如果执行失败返回空字符串
     */
    public String executeCommandIgnoreError(String... command) {
        try {
            return executeCommand(command);
        } catch (Exception e) {
            log.warn("执行命令失败（忽略错误）: command={}", String.join(" ", command), e);
            return "";
        }
    }
    
    /**
     * 检查命令是否可用
     * 
     * @param command 命令名称
     * @return 是否可用
     */
    public boolean isCommandAvailable(String command) {
        try {
            executeCommand("which", command);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 执行shell脚本
     * 
     * @param scriptPath 脚本路径
     * @param args 脚本参数
     * @return 执行结果
     * @throws IOException 执行异常
     * @throws InterruptedException 中断异常
     */
    public String executeScript(String scriptPath, String... args) throws IOException, InterruptedException {
        String[] command = new String[args.length + 2];
        command[0] = "bash";
        command[1] = scriptPath;
        System.arraycopy(args, 0, command, 2, args.length);
        
        return executeCommand(command);
    }
}
