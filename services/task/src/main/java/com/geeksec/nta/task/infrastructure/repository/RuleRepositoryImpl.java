package com.geeksec.nta.task.infrastructure.repository;

import com.geeksec.nta.task.domain.repository.RuleRepository;
import com.geeksec.nta.task.domain.valueobject.FilterInfo;
import com.geeksec.nta.task.domain.valueobject.RuleInfo;
import com.geeksec.nta.task.infrastructure.mapper.RuleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 规则仓储实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class RuleRepositoryImpl implements RuleRepository {
    
    private final RuleMapper ruleMapper;
    
    @Override
    public List<RuleInfo> findActiveRulesByTaskId(Integer taskId) {
        return ruleMapper.findActiveRulesByTaskId(taskId);
    }
    
    @Override
    public Optional<RuleInfo> findByRuleId(Integer ruleId) {
        RuleInfo ruleInfo = ruleMapper.findByRuleId(ruleId);
        return ruleInfo != null ? Optional.of(ruleInfo) : Optional.empty();
    }
    
    @Override
    public List<FilterInfo> findFilterConfigByTaskId(Integer taskId) {
        return ruleMapper.findFilterConfigByTaskId(taskId);
    }
    
    @Override
    public Optional<Integer> findFilterStateByTaskId(Integer taskId) {
        Integer state = ruleMapper.findFilterStateByTaskId(taskId);
        return state != null ? Optional.of(state) : Optional.empty();
    }
    
    @Override
    public Optional<String> findFullflowStateByBatchId(Integer batchId) {
        String state = ruleMapper.findFullflowStateByBatchId(batchId);
        return state != null ? Optional.of(state) : Optional.empty();
    }
    
    @Override
    public Optional<String> findFlowlogStateByBatchId(Integer batchId) {
        String state = ruleMapper.findFlowlogStateByBatchId(batchId);
        return state != null ? Optional.of(state) : Optional.empty();
    }
    
    @Override
    public Optional<BatchFlowConfig> findFlowConfigByBatchId(Integer batchId) {
        BatchFlowConfig config = ruleMapper.findFlowConfigByBatchId(batchId);
        return config != null ? Optional.of(config) : Optional.empty();
    }
}
