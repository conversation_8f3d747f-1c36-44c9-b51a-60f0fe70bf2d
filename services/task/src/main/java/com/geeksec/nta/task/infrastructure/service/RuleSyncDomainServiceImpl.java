package com.geeksec.nta.task.infrastructure.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.nta.task.domain.service.RuleSyncDomainService;
import com.geeksec.nta.task.domain.valueobject.RuleInfo;
import com.geeksec.nta.task.domain.valueobject.FilterInfo;
import com.geeksec.nta.task.domain.repository.RuleRepository;
import com.geeksec.nta.task.infrastructure.util.SystemCommandUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则同步领域服务实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RuleSyncDomainServiceImpl implements RuleSyncDomainService {
    
    private final RuleRepository ruleRepository;
    private final SystemCommandUtil systemCommandUtil;
    private final ObjectMapper objectMapper;
    
    private boolean isFullFlow = true;
    private StringBuilder jsonContext = new StringBuilder();
    
    @Override
    public void syncRules(Integer taskId, Integer batchId, Path taskPath) {
        log.info("开始同步规则: taskId={}, batchId={}, taskPath={}", taskId, batchId, taskPath);
        
        try {
            // 检查全流量状态
            checkFullFlowState(batchId);
            
            // 获取有效规则
            List<RuleInfo> activeRules = getActiveRules(taskId);
            
            // 创建规则文件
            Path ruleFilePath = taskPath.resolve("rule.json");
            createRuleFile(activeRules, ruleFilePath, taskPath);
            
            // 创建规则标签文件
            Path ruleTagPath = taskPath.resolve("ruletag.json");
            Files.writeString(ruleTagPath, jsonContext.toString());
            
            // 复制规则文件到用户规则目录
            Path userRuleDir = taskPath.resolve("JsonRule/BasicRule/UserRule");
            Files.createDirectories(userRuleDir);
            Files.copy(ruleFilePath, userRuleDir.resolve("rule.json"));
            
            log.info("规则同步完成: taskId={}, ruleCount={}", taskId, activeRules.size());
            
        } catch (Exception e) {
            log.error("同步规则失败: taskId={}, batchId={}", taskId, batchId, e);
            throw new RuntimeException("同步规则失败", e);
        }
    }
    
    @Override
    public void syncFilters(Integer taskId, Path taskPath) {
        log.info("开始同步过滤器: taskId={}, taskPath={}", taskId, taskPath);
        
        try {
            // 获取过滤器配置
            List<FilterInfo> filterConfigs = getFilterConfig(taskId);
            
            // 构建配置JSON
            ObjectNode configJson = buildBaseConfig();
            ObjectNode filterJson = objectMapper.createObjectNode();
            
            if (filterConfigs.isEmpty()) {
                // 没有过滤器配置，使用默认状态
                Integer filterState = ruleRepository.findFilterStateByTaskId(taskId).orElse(0);
                String response = filterState == 0 ? "pass" : "drop";
                filterJson.put("Respond", response);
                filterJson.set("Rule", objectMapper.createArrayNode());
            } else {
                // 有过滤器配置
                String response = filterConfigs.get(0).getResponseType();
                filterJson.put("Respond", response);
                
                var ruleArray = objectMapper.createArrayNode();
                for (FilterInfo filterInfo : filterConfigs) {
                    JsonNode filterRule = objectMapper.readTree(filterInfo.getFilterJson());
                    ((ObjectNode) filterRule).put("ID", filterInfo.getId());
                    
                    // 移除空的IP字段
                    if (filterRule.has("ip") && filterRule.get("ip").asText().isEmpty()) {
                        ((ObjectNode) filterRule).remove("ip");
                    }
                    
                    ruleArray.add(filterRule);
                }
                filterJson.set("Rule", ruleArray);
            }
            
            configJson.set("Filter", filterJson);
            
            // 写入配置文件
            Path configFile = taskPath.resolve("Config/Config.txt");
            Files.createDirectories(configFile.getParent());
            Files.writeString(configFile, objectMapper.writeValueAsString(configJson));
            
            log.info("过滤器同步完成: taskId={}, filterCount={}", taskId, filterConfigs.size());
            
        } catch (Exception e) {
            log.error("同步过滤器失败: taskId={}", taskId, e);
            throw new RuntimeException("同步过滤器失败", e);
        }
    }
    
    @Override
    public void syncFullflowAndFlowlog(Integer batchId, Path taskPath) {
        log.info("开始同步全流量和流日志配置: batchId={}, taskPath={}", batchId, taskPath);
        
        try {
            var flowConfig = ruleRepository.findFlowConfigByBatchId(batchId);
            if (flowConfig.isEmpty()) {
                log.warn("未找到批次流配置: batchId={}", batchId);
                return;
            }
            
            var config = flowConfig.get();
            
            // 更新write_pcap.xml
            updateWritePcapXml(taskPath, config.getFullflowState());
            
            // 更新plugin_conf.json
            updatePluginConf(taskPath, config.getFlowlogState());
            
            log.info("全流量和流日志配置同步完成: batchId={}", batchId);
            
        } catch (Exception e) {
            log.error("同步全流量和流日志配置失败: batchId={}", batchId, e);
            throw new RuntimeException("同步全流量和流日志配置失败", e);
        }
    }
    
    @Override
    public List<RuleInfo> getActiveRules(Integer taskId) {
        return ruleRepository.findActiveRulesByTaskId(taskId);
    }
    
    @Override
    public List<FilterInfo> getFilterConfig(Integer taskId) {
        return ruleRepository.findFilterConfigByTaskId(taskId);
    }
    
    @Override
    public void createRuleFile(List<RuleInfo> rules, Path ruleFilePath, Path taskPath) {
        try {
            StringBuilder content = new StringBuilder();
            jsonContext.setLength(0); // 清空之前的内容
            
            for (RuleInfo rule : rules) {
                if (!"生效".equals(rule.getRuleState())) {
                    continue;
                }
                
                JsonNode ruleJson = objectMapper.readTree(rule.getRuleJson());
                ((ObjectNode) ruleJson).put("Name", rule.getRuleName());
                
                // 处理动态库规则
                if (rule.isDynamicLibRule()) {
                    processDynamicLibRule(rule, taskPath);
                    ((ObjectNode) ruleJson).put("Lib", rule.getLibRespondLib());
                }
                
                // 处理复杂规则
                if (rule.isComplexRule()) {
                    processComplexRule(rule, taskPath);
                    String libName = rule.getRuleId() + ".so";
                    ((ObjectNode) ruleJson).put("Lib", libName);
                }
                
                // 构建规则标签
                Map<String, Object> ruleTag = buildRuleTag(rule);
                jsonContext.append("\n\n").append(objectMapper.writeValueAsString(ruleTag));
                
                // 移除敏感字段
                ((ObjectNode) ruleJson).remove("lib_data_so");
                ((ObjectNode) ruleJson).remove("lib_data_conf");
                
                content.append("\n\n").append(objectMapper.writeValueAsString(ruleJson));
                
                log.debug("处理规则: ruleId={}, ruleName={}", rule.getRuleId(), rule.getRuleName());
            }
            
            Files.writeString(ruleFilePath, content.toString());
            
        } catch (Exception e) {
            log.error("创建规则文件失败: ruleFilePath={}", ruleFilePath, e);
            throw new RuntimeException("创建规则文件失败", e);
        }
    }
    
    @Override
    public void processDynamicLibRule(RuleInfo rule, Path taskPath) {
        try {
            // 创建动态库目录
            Path libDir = taskPath.resolve("JsonRule/BasicRule/LibFolder");
            Files.createDirectories(libDir);
            
            // 写入动态库文件
            String libName = rule.getLibRespondLib();
            Path libFile = libDir.resolve(libName);
            byte[] libData = Base64.getDecoder().decode(rule.getLibDataSo());
            Files.write(libFile, libData);
            
            // 处理配置文件
            if (rule.getLibRespondConfig() != null && !rule.getLibRespondConfig().isEmpty()) {
                Path confDir = taskPath.resolve("JsonRule/BasicRule/LibConfig/" + rule.getRuleId());
                Files.createDirectories(confDir);
                
                Path confFile = confDir.resolve(rule.getLibRespondConfig());
                byte[] confData = Base64.getDecoder().decode(rule.getLibDataConf());
                Files.write(confFile, confData);
            }
            
        } catch (Exception e) {
            log.error("处理动态库规则失败: ruleId={}", rule.getRuleId(), e);
            throw new RuntimeException("处理动态库规则失败", e);
        }
    }
    
    @Override
    public void processComplexRule(RuleInfo rule, Path taskPath) {
        try {
            // 创建临时配置文件
            String tempConfName = "/tmp/" + rule.getRuleId() + ".json";
            JsonNode ruleJson = objectMapper.readTree(rule.getRuleJson());
            JsonNode detailRespond = ruleJson.get("DetailRespond");
            Files.writeString(Path.of(tempConfName), objectMapper.writeValueAsString(detailRespond));
            
            // 创建动态库目录
            Path libDir = taskPath.resolve("JsonRule/BasicRule/LibFolder");
            Files.createDirectories(libDir);
            
            String libName = rule.getRuleId() + ".so";
            Path libFile = libDir.resolve(libName);
            
            // 执行复杂规则生成脚本
            systemCommandUtil.executeScript(
                "/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh",
                tempConfName,
                libName
            );
            
            // 移动生成的动态库文件
            Path tempLibFile = Path.of("/tmp/" + libName);
            Files.move(tempLibFile, libFile);
            
            // 清理临时文件
            Files.deleteIfExists(Path.of(tempConfName));
            
        } catch (Exception e) {
            log.error("处理复杂规则失败: ruleId={}", rule.getRuleId(), e);
            throw new RuntimeException("处理复杂规则失败", e);
        }
    }
    
    /**
     * 检查全流量状态
     */
    private void checkFullFlowState(Integer batchId) {
        String fullflowState = ruleRepository.findFullflowStateByBatchId(batchId).orElse("ON");
        isFullFlow = !"OFF".equals(fullflowState);
    }
    
    /**
     * 构建基础配置
     */
    private ObjectNode buildBaseConfig() throws IOException {
        String configStr = """
            {"Basic":{"DeviceNO":"123-456","Mission":"testn","OfflineFolder":"./OfflineFolder","FirstPro":12},"PFRing":{"Interface":["wlp3s0"],"ThreadNum":2,"RenewInterval_Sec":120,"CPU":[1,2,3,4],"BalanceCore":11},"Connect":{"IsIP":1,"IsUDP":1,"SYNSign":0},"ConnectInfor":{"ConnectNum_IPv4":10000,"ConnectNum_IPv6":1232,"TimeInterval":90},"LanProbe":{"FirstPro":[10,12,113,277],"SaveType_Pcap":1,"StartTime":"2018-01-19 16:51:00","LogFolder":"./FlowLog","RuleFolder":"./JsonRule/BasicRule","EngineFolder":"./JsonRule/BasicEngine","SaveFolder":"./PcapFolder","SaveFlow":0,"SaveFirstPacket":1,"RuleLog_Folder":"./FlowLog/RuleLog","RuleLog_MinLevel":20},"TileraFrame":{"PageNum":1,"rx":["eth0","eth1"],"GroupNumOfEachLink":8,"BucketNumOfEachLink":8,"RingNumOfEachLink":8,"BucketMode":1,"NodeOfEachRing":65536},"Extract_HTTP_Title":["^Sec-WebSocket","^Get","^Post","^HTTP","^Referer","^User-","^Server","^Date","^Cookie","^Host","^Last-Modified","^Expires","^Content","^Connect","^Accept","^Access","^Origin","^x-xss","^x-content","^x-frame","^strict-transport","^public-key","^Age","^ETag","^Location","^Proxy","^Retry","^Vary","^www-Auth","^upgrade"],"Extract":{"LogFolder":"./FlowLog/Extract","PcapFolder":"./PcapFolder/Extract_AbnormalPacket","CertFolder":"./FlowLog/Cert","BlockNum":1024,"IsConnect":1,"IsDNSQuery":1,"IsDNS":1,"IsHTTP":1,"IsTSL":1},"ProtocolAnalyse":{"SaveFolder":"./PcapFolder/ProAnalyse","TLS_HandShake":1,"AbnormalPro":1,"AbnormalIPTTL":5},"KeepOrder":{"PacketNum":10},"Port53":{"IPNum":50000,"Folder":"./FlowLog/Port53","TimeInterval":86400},"BytesDistribute":{"MaxBytes":2147483648,"MinBytes":2048,"Duration":60},"PlusIn_Json":{},"IPStatistic":{"VIP":[],"Network":[],"DMZ":[],"SaveFolder":"./PcapFolder","MacNum":1000,"IPv4Num":10000,"IPv6Num":5000,"TimeInterval_IPClear":1600,"TimeInterval_IPInfor":120,"SYNSample":1048575,"DDOS":{"LogFolder":"./FlowLog/DDOS_Log","TimeInterval_Judge":30,"Threshold_Packet":1000,"Times_SYN_Judge":4,"Times_Connect_Judge":4,"Hour_BasicLine_Judge":24,"Times_BasicLine_Judge":10},"IntraIPNum":0,"MacNum_Extra":0},"DDOS_Defense":{"Type":4,"RenewTimeInterval":30,"IntraIPv4":[{"IP":"***********","Mask":"***********"}],"IntraMac":[],"IPv4Num":200000,"IPv6Num":5000,"DDOS":{"TimeInterval_Judge":6,"Times_TCP":20,"Times_DNS":20,"Times_ICMP":20,"Times_UDP":20,"mbps_Basic":10,"PacketNum_Basic":1000}}}
            """;
        return (ObjectNode) objectMapper.readTree(configStr);
    }
    
    /**
     * 构建规则标签
     */
    private Map<String, Object> buildRuleTag(RuleInfo rule) {
        Map<String, Object> ruleTag = new HashMap<>();
        ruleTag.put("RuleId", rule.getRuleId());
        ruleTag.put("rule_level", rule.getRuleLevel());
        ruleTag.put("RuleName", rule.getRuleName());
        ruleTag.put("Tag", List.of());
        ruleTag.put("Infor", rule.getRuleDesc());
        ruleTag.put("rule_size", rule.getRuleSize());
        ruleTag.put("capture_mode", rule.getCaptureMode());
        ruleTag.put("created_time", rule.getCreatedTime().toString());
        ruleTag.put("PbDrop", rule.getPbDrop());
        ruleTag.put("PcapDrop", rule.getPcapDrop());
        ruleTag.put("updated_time", rule.getUpdatedTime().toString());
        return ruleTag;
    }
    
    /**
     * 更新write_pcap.xml文件
     */
    private void updateWritePcapXml(Path taskPath, String fullflowState) throws IOException {
        Path xmlFile = taskPath.resolve("write_pcap.xml");
        if (!Files.exists(xmlFile)) {
            log.warn("write_pcap.xml文件不存在: {}", xmlFile);
            return;
        }
        
        String content = Files.readString(xmlFile);
        boolean saveRule = "OFF".equals(fullflowState);
        content = content.replaceAll("<b_rule_save>.*?</b_rule_save>", 
            "<b_rule_save>" + saveRule + "</b_rule_save>");
        Files.writeString(xmlFile, content);
    }
    
    /**
     * 更新plugin_conf.json文件
     */
    private void updatePluginConf(Path taskPath, String flowlogState) throws IOException {
        Path jsonFile = taskPath.resolve("plugin_conf.json");
        if (!Files.exists(jsonFile)) {
            log.warn("plugin_conf.json文件不存在: {}", jsonFile);
            return;
        }
        
        JsonNode data = objectMapper.readTree(Files.readString(jsonFile));
        int shouldLogValue = "ON".equals(flowlogState) ? 1 : 0;
        
        ((ObjectNode) data.get("proto_parse")).put("should_log_def", shouldLogValue);
        
        JsonNode plugins = data.get("proto_parse").get("plugin");
        if (plugins.isArray()) {
            for (JsonNode plugin : plugins) {
                ((ObjectNode) plugin).put("should_log", shouldLogValue);
            }
        }
        
        Files.writeString(jsonFile, objectMapper.writeValueAsString(data));
    }
}
