package com.geeksec.nta.task.interfaces.rest;

import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.application.service.PcapImportApplicationService;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * PCAP导入控制器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@RestController
@RequestMapping("/pcap-import")
@RequiredArgsConstructor
@Slf4j
@Validated
public class PcapImportController {

    private final PcapImportApplicationService pcapImportApplicationService;

    /**
     * 手动触发PCAP导入（同步处理）
     */
    @PostMapping("/process")
    public ResponseEntity<String> processImport(
            @Valid @RequestBody PcapImportRequestDto request) {

        log.info("接收到手动PCAP导入请求: {}", request);

        try {
            pcapImportApplicationService.processImportRequest(request);
            return ResponseEntity.ok("PCAP导入请求处理成功");
        } catch (Exception e) {
            log.error("处理PCAP导入请求失败", e);
            return ResponseEntity.internalServerError()
                .body("处理PCAP导入请求失败: " + e.getMessage());
        }
    }

    /**
     * 异步触发PCAP导入
     */
    @PostMapping("/process-async")
    public ResponseEntity<String> processImportAsync(
            @Valid @RequestBody PcapImportRequestDto request) {

        log.info("接收到异步PCAP导入请求: {}", request);

        try {
            // 异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    pcapImportApplicationService.processImportRequest(request);
                    log.info("异步PCAP导入请求处理完成: taskId={}, batchId={}",
                        request.getTaskId(), request.getBatchId());
                } catch (Exception e) {
                    log.error("异步处理PCAP导入请求失败: taskId={}, batchId={}",
                        request.getTaskId(), request.getBatchId(), e);
                }
            });

            return ResponseEntity.accepted()
                .body("PCAP导入请求已接收，正在异步处理");
        } catch (Exception e) {
            log.error("提交异步PCAP导入请求失败", e);
            return ResponseEntity.internalServerError()
                .body("提交异步PCAP导入请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务状态
     */
    @GetMapping("/status")
    public ResponseEntity<PcapImportTask> getTaskStatus(
            @RequestParam("taskId") Integer taskId,
            @RequestParam("batchId") Integer batchId) {

        Optional<PcapImportTask> task = pcapImportApplicationService.getTaskStatus(taskId, batchId);

        if (task.isPresent()) {
            return ResponseEntity.ok(task.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 查询待处理任务列表
     */
    @GetMapping("/pending")
    public ResponseEntity<List<PcapImportTask>> getPendingTasks() {
        List<PcapImportTask> pendingTasks = pcapImportApplicationService.getPendingTasks();
        return ResponseEntity.ok(pendingTasks);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("PCAP导入服务运行正常");
    }
}
