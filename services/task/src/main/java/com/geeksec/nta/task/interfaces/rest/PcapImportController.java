package com.geeksec.nta.task.interfaces.rest;

import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.application.service.PcapImportApplicationService;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * PCAP导入控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@RestController
@RequestMapping("/pcap-import")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "PCAP导入管理")
public class PcapImportController {
    
    private final PcapImportApplicationService pcapImportApplicationService;
    
    /**
     * 手动触发PCAP导入
     */
    @PostMapping("/process")
    @ApiOperation("手动触发PCAP导入")
    public ResponseEntity<String> processImport(
            @Valid @RequestBody PcapImportRequestDto request) {
        
        log.info("接收到手动PCAP导入请求: {}", request);
        
        try {
            pcapImportApplicationService.processImportRequest(request);
            return ResponseEntity.ok("PCAP导入请求处理成功");
        } catch (Exception e) {
            log.error("处理PCAP导入请求失败", e);
            return ResponseEntity.internalServerError()
                .body("处理PCAP导入请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务状态
     */
    @GetMapping("/status")
    @ApiOperation("查询任务状态")
    public ResponseEntity<PcapImportTask> getTaskStatus(
            @ApiParam("任务ID") @RequestParam Integer taskId,
            @ApiParam("批次ID") @RequestParam Integer batchId) {
        
        Optional<PcapImportTask> task = pcapImportApplicationService.getTaskStatus(taskId, batchId);
        
        if (task.isPresent()) {
            return ResponseEntity.ok(task.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 查询待处理任务列表
     */
    @GetMapping("/pending")
    @ApiOperation("查询待处理任务列表")
    public ResponseEntity<List<PcapImportTask>> getPendingTasks() {
        List<PcapImportTask> pendingTasks = pcapImportApplicationService.getPendingTasks();
        return ResponseEntity.ok(pendingTasks);
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("PCAP导入服务运行正常");
    }
}
