package com.geeksec.nta.task.domain.service;

import com.geeksec.nta.task.domain.entity.ServiceInfo;
import com.geeksec.nta.task.domain.enums.ServiceStatus;

import java.util.List;
import java.util.Optional;

/**
 * 服务管理领域服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface ServiceManagementDomainService {
    
    /**
     * 检查服务状态
     * 
     * @param serviceName 服务名称
     * @return 服务状态
     */
    ServiceStatus checkServiceStatus(String serviceName);
    
    /**
     * 创建服务
     * 
     * @param serviceId 服务ID
     * @return 创建的服务信息
     */
    ServiceInfo createService(Integer serviceId);
    
    /**
     * 查找空闲服务
     * 
     * @param serviceIdList 可用服务ID列表
     * @return 空闲服务ID，如果没有则返回空
     */
    Optional<Integer> findIdleService(List<Integer> serviceIdList);
    
    /**
     * 启动服务
     * 
     * @param serviceName 服务名称
     * @return 是否启动成功
     */
    boolean startService(String serviceName);
    
    /**
     * 停止服务
     * 
     * @param serviceName 服务名称
     * @return 是否停止成功
     */
    boolean stopService(String serviceName);
    
    /**
     * 重启服务
     * 
     * @param serviceName 服务名称
     * @return 是否重启成功
     */
    boolean restartService(String serviceName);
    
    /**
     * 获取服务信息
     * 
     * @param serviceId 服务ID
     * @return 服务信息
     */
    Optional<ServiceInfo> getServiceInfo(Integer serviceId);
    
    /**
     * 获取所有服务信息
     * 
     * @param serviceIdList 服务ID列表
     * @return 服务信息列表
     */
    List<ServiceInfo> getAllServiceInfo(List<Integer> serviceIdList);
}
