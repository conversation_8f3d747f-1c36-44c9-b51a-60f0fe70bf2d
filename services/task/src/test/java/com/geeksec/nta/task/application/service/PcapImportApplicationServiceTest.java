package com.geeksec.nta.task.application.service;

import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import com.geeksec.nta.task.domain.service.ServiceManagementDomainService;
import com.geeksec.nta.task.domain.service.RuleSyncDomainService;
import com.geeksec.nta.task.domain.repository.PcapImportTaskRepository;
import com.geeksec.nta.task.infrastructure.util.FileUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PCAP导入应用服务测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class PcapImportApplicationServiceTest {
    
    @Mock
    private ServiceManagementDomainService serviceManagementDomainService;
    
    @Mock
    private RuleSyncDomainService ruleSyncDomainService;
    
    @Mock
    private PcapImportTaskRepository pcapImportTaskRepository;
    
    @Mock
    private FileUtil fileUtil;
    
    @InjectMocks
    private PcapImportApplicationService pcapImportApplicationService;
    
    private PcapImportRequestDto testRequest;
    private List<String> testPcapFiles;
    
    @BeforeEach
    void setUp() {
        // 设置服务ID列表
        ReflectionTestUtils.setField(pcapImportApplicationService, "serviceIdList", Arrays.asList(11, 12, 13));
        
        // 准备测试数据
        testRequest = new PcapImportRequestDto();
        testRequest.setTaskId(1001);
        testRequest.setBatchId(2001);
        testRequest.setInputDirs(Arrays.asList("/data/pcap/test1", "/data/pcap/test2"));
        
        testPcapFiles = Arrays.asList(
            "/data/pcap/test1/file1.pcap",
            "/data/pcap/test1/file2.pcap",
            "/data/pcap/test2/file3.pcap"
        );
    }
    
    @Test
    void testProcessImportRequest_Success() {
        // Given
        when(fileUtil.findPcapFiles(testRequest.getInputDirs())).thenReturn(testPcapFiles);
        when(serviceManagementDomainService.findIdleService(anyList())).thenReturn(Optional.of(11));
        when(serviceManagementDomainService.restartService(anyString())).thenReturn(true);
        when(pcapImportTaskRepository.save(any(PcapImportTask.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        assertDoesNotThrow(() -> pcapImportApplicationService.processImportRequest(testRequest));
        
        // Then
        verify(fileUtil).findPcapFiles(testRequest.getInputDirs());
        verify(pcapImportTaskRepository).updatePcapNum(testRequest.getBatchId(), testPcapFiles.size());
        verify(serviceManagementDomainService).findIdleService(anyList());
        verify(pcapImportTaskRepository, times(2)).save(any(PcapImportTask.class));
        verify(fileUtil).writeTaskInfo(any(), any(PcapImportTask.class));
        verify(fileUtil).writeFileIndex(any(), eq(testPcapFiles));
        verify(ruleSyncDomainService).syncFilters(eq(testRequest.getTaskId()), any());
        verify(ruleSyncDomainService).syncRules(eq(testRequest.getTaskId()), eq(testRequest.getBatchId()), any());
        verify(ruleSyncDomainService).syncFullflowAndFlowlog(eq(testRequest.getBatchId()), any());
        verify(pcapImportTaskRepository).insertBatchOfflineThd(
            eq(testRequest.getTaskId()), 
            eq(testRequest.getBatchId()), 
            eq(11), 
            eq("offline_thd.11.service")
        );
        verify(serviceManagementDomainService).restartService("offline_thd.11.service");
    }
    
    @Test
    void testProcessImportRequest_NoIdleService() {
        // Given
        when(fileUtil.findPcapFiles(testRequest.getInputDirs())).thenReturn(testPcapFiles);
        when(serviceManagementDomainService.findIdleService(anyList())).thenReturn(Optional.empty());
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> pcapImportApplicationService.processImportRequest(testRequest));
        
        assertEquals("处理PCAP导入请求失败", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("没有可用的空闲服务"));
    }
    
    @Test
    void testProcessImportRequest_ServiceStartFailed() {
        // Given
        when(fileUtil.findPcapFiles(testRequest.getInputDirs())).thenReturn(testPcapFiles);
        when(serviceManagementDomainService.findIdleService(anyList())).thenReturn(Optional.of(11));
        when(serviceManagementDomainService.restartService(anyString())).thenReturn(false);
        when(pcapImportTaskRepository.save(any(PcapImportTask.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> pcapImportApplicationService.processImportRequest(testRequest));
        
        assertEquals("处理PCAP导入请求失败", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("服务启动失败"));
    }
    
    @Test
    void testGetTaskStatus_Found() {
        // Given
        PcapImportTask expectedTask = new PcapImportTask();
        expectedTask.setTaskId(testRequest.getTaskId());
        expectedTask.setBatchId(testRequest.getBatchId());
        expectedTask.setStatus("PROCESSING");
        
        when(pcapImportTaskRepository.findByTaskIdAndBatchId(testRequest.getTaskId(), testRequest.getBatchId()))
            .thenReturn(Optional.of(expectedTask));
        
        // When
        Optional<PcapImportTask> result = pcapImportApplicationService.getTaskStatus(
            testRequest.getTaskId(), testRequest.getBatchId());
        
        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedTask.getTaskId(), result.get().getTaskId());
        assertEquals(expectedTask.getBatchId(), result.get().getBatchId());
        assertEquals(expectedTask.getStatus(), result.get().getStatus());
    }
    
    @Test
    void testGetTaskStatus_NotFound() {
        // Given
        when(pcapImportTaskRepository.findByTaskIdAndBatchId(testRequest.getTaskId(), testRequest.getBatchId()))
            .thenReturn(Optional.empty());
        
        // When
        Optional<PcapImportTask> result = pcapImportApplicationService.getTaskStatus(
            testRequest.getTaskId(), testRequest.getBatchId());
        
        // Then
        assertFalse(result.isPresent());
    }
    
    @Test
    void testGetPendingTasks() {
        // Given
        List<PcapImportTask> expectedTasks = Arrays.asList(
            new PcapImportTask(1001, 2001, Arrays.asList("/data/test1")),
            new PcapImportTask(1002, 2002, Arrays.asList("/data/test2"))
        );
        
        when(pcapImportTaskRepository.findPendingTasks()).thenReturn(expectedTasks);
        
        // When
        List<PcapImportTask> result = pcapImportApplicationService.getPendingTasks();
        
        // Then
        assertEquals(expectedTasks.size(), result.size());
        assertEquals(expectedTasks, result);
    }
}
