package com.geeksec.nta.task.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.task.application.dto.PcapImportRequestDto;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import com.geeksec.nta.task.infrastructure.mapper.PcapImportTaskMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PCAP导入集成测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class PcapImportIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    

    
    @MockBean
    private PcapImportTaskMapper pcapImportTaskMapper;
    
    @Test
    void testProcessImportRequest_Success() throws Exception {
        // Given
        PcapImportRequestDto request = new PcapImportRequestDto();
        request.setTaskId(1001);
        request.setBatchId(2001);
        request.setInputDirs(Arrays.asList("/data/test1", "/data/test2"));
        
        // Mock external dependencies
        when(pcapImportTaskMapper.findByTaskIdAndBatchId(any(), any())).thenReturn(null);
        
        // When & Then
        mockMvc.perform(post("/pcap-import/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("PCAP导入请求处理成功"));
    }
    
    @Test
    void testProcessImportRequest_ValidationError() throws Exception {
        // Given - 缺少必填字段的请求
        PcapImportRequestDto request = new PcapImportRequestDto();
        request.setTaskId(null); // 缺少taskId
        request.setBatchId(2001);
        request.setInputDirs(Arrays.asList("/data/test1"));
        
        // When & Then
        mockMvc.perform(post("/pcap-import/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testGetTaskStatus_Found() throws Exception {
        // Given
        Integer taskId = 1001;
        Integer batchId = 2001;
        
        PcapImportTask mockTask = new PcapImportTask();
        mockTask.setTaskId(taskId);
        mockTask.setBatchId(batchId);
        mockTask.setStatus("PROCESSING");
        
        // Mock repository response
        when(pcapImportTaskMapper.findByTaskIdAndBatchId(taskId, batchId))
                .thenReturn(convertToPO(mockTask));
        
        // When & Then
        mockMvc.perform(get("/pcap-import/status")
                .param("taskId", taskId.toString())
                .param("batchId", batchId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId))
                .andExpect(jsonPath("$.batchId").value(batchId))
                .andExpect(jsonPath("$.status").value("PROCESSING"));
    }
    
    @Test
    void testGetTaskStatus_NotFound() throws Exception {
        // Given
        Integer taskId = 1001;
        Integer batchId = 2001;
        
        when(pcapImportTaskMapper.findByTaskIdAndBatchId(taskId, batchId))
                .thenReturn(null);
        
        // When & Then
        mockMvc.perform(get("/pcap-import/status")
                .param("taskId", taskId.toString())
                .param("batchId", batchId.toString()))
                .andExpect(status().isNotFound());
    }
    
    @Test
    void testGetPendingTasks() throws Exception {
        // Given
        when(pcapImportTaskMapper.findPendingTasks())
                .thenReturn(Arrays.asList());
        
        // When & Then
        mockMvc.perform(get("/pcap-import/pending"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }
    
    @Test
    void testHealthCheck() throws Exception {
        // When & Then
        mockMvc.perform(get("/pcap-import/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("PCAP导入服务运行正常"));
    }
    
    /**
     * 辅助方法：将实体转换为PO（简化版本）
     */
    private com.geeksec.nta.task.infrastructure.po.PcapImportTaskPO convertToPO(PcapImportTask task) {
        com.geeksec.nta.task.infrastructure.po.PcapImportTaskPO po = 
            new com.geeksec.nta.task.infrastructure.po.PcapImportTaskPO();
        po.setTaskId(task.getTaskId());
        po.setBatchId(task.getBatchId());
        po.setStatus(task.getStatus());
        po.setCreatedTime(task.getCreatedTime());
        po.setStartTime(task.getStartTime());
        po.setFinishTime(task.getFinishTime());
        po.setErrorMessage(task.getErrorMessage());
        return po;
    }
}
