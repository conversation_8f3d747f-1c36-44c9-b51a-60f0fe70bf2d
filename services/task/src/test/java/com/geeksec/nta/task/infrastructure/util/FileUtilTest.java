package com.geeksec.nta.task.infrastructure.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.task.domain.entity.PcapImportTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件工具类测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
class FileUtilTest {
    
    private FileUtil fileUtil;
    private ObjectMapper objectMapper;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        fileUtil = new FileUtil(objectMapper);
    }
    
    @Test
    void testFindPcapFiles_WithFiles() throws IOException {
        // Given
        Path pcapFile1 = tempDir.resolve("test1.pcap");
        Path pcapFile2 = tempDir.resolve("test2.cap");
        Path pcapFile3 = tempDir.resolve("test3.pcapng");
        Path txtFile = tempDir.resolve("test.txt");
        
        Files.createFile(pcapFile1);
        Files.createFile(pcapFile2);
        Files.createFile(pcapFile3);
        Files.createFile(txtFile);
        
        List<String> inputDirs = Arrays.asList(
            pcapFile1.toString(),
            pcapFile2.toString(),
            pcapFile3.toString(),
            txtFile.toString()
        );
        
        // When
        List<String> result = fileUtil.findPcapFiles(inputDirs);
        
        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(pcapFile1.toString()));
        assertTrue(result.contains(pcapFile2.toString()));
        assertTrue(result.contains(pcapFile3.toString()));
        assertFalse(result.contains(txtFile.toString()));
    }
    
    @Test
    void testFindPcapFiles_WithDirectory() throws IOException {
        // Given
        Path subDir = tempDir.resolve("subdir");
        Files.createDirectories(subDir);
        
        Path pcapFile1 = tempDir.resolve("test1.pcap");
        Path pcapFile2 = subDir.resolve("test2.pcap");
        Path txtFile = tempDir.resolve("test.txt");
        
        Files.createFile(pcapFile1);
        Files.createFile(pcapFile2);
        Files.createFile(txtFile);
        
        List<String> inputDirs = Arrays.asList(tempDir.toString());
        
        // When
        List<String> result = fileUtil.findPcapFiles(inputDirs);
        
        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(pcapFile1.toString()));
        assertTrue(result.contains(pcapFile2.toString()));
    }
    
    @Test
    void testWriteTaskInfo() throws IOException {
        // Given
        Path configPath = tempDir.resolve("config");
        PcapImportTask task = new PcapImportTask();
        task.setTaskId(1001);
        task.setBatchId(2001);
        
        // When
        fileUtil.writeTaskInfo(configPath, task);
        
        // Then
        Path taskInfoFile = configPath.resolve("task_info.json");
        assertTrue(Files.exists(taskInfoFile));
        
        String content = Files.readString(taskInfoFile);
        assertTrue(content.contains("\"task_id\":1001"));
        assertTrue(content.contains("\"batch_id\":2001"));
    }
    
    @Test
    void testWriteFileIndex() throws IOException {
        // Given
        Path configPath = tempDir.resolve("config");
        Files.createDirectories(configPath);
        
        List<String> pcapFiles = Arrays.asList(
            "/data/test1.pcap",
            "/data/test2.pcap",
            "/data/test3.pcap"
        );
        
        // When
        fileUtil.writeFileIndex(configPath, pcapFiles);
        
        // Then
        Path fileIndexPath = configPath.resolve("file_index.txt");
        assertTrue(Files.exists(fileIndexPath));
        
        String content = Files.readString(fileIndexPath);
        assertTrue(content.contains("/data/test1.pcap"));
        assertTrue(content.contains("/data/test2.pcap"));
        assertTrue(content.contains("/data/test3.pcap"));
        assertTrue(content.contains("end.pcap"));
        
        // 检查任务PCAP读取配置文件是否被清空
        Path taskPcapReadConf = configPath.resolve("task_pcap_read.conf");
        assertTrue(Files.exists(taskPcapReadConf));
        assertEquals("", Files.readString(taskPcapReadConf));
    }
    
    @Test
    void testWriteFile() throws IOException {
        // Given
        Path filePath = tempDir.resolve("test.txt");
        String content = "Hello, World!";
        
        // When
        fileUtil.writeFile(filePath, content);
        
        // Then
        assertTrue(Files.exists(filePath));
        assertEquals(content, Files.readString(filePath));
    }
    
    @Test
    void testWriteBase64File() throws IOException {
        // Given
        Path filePath = tempDir.resolve("test.bin");
        String originalContent = "Hello, World!";
        String base64Content = Base64.getEncoder().encodeToString(originalContent.getBytes());
        
        // When
        fileUtil.writeBase64File(filePath, base64Content);
        
        // Then
        assertTrue(Files.exists(filePath));
        byte[] decodedBytes = Files.readAllBytes(filePath);
        assertEquals(originalContent, new String(decodedBytes));
    }
    
    @Test
    void testReadFile() throws IOException {
        // Given
        Path filePath = tempDir.resolve("test.txt");
        String content = "Hello, World!";
        Files.writeString(filePath, content);
        
        // When
        String result = fileUtil.readFile(filePath);
        
        // Then
        assertEquals(content, result);
    }
    
    @Test
    void testCopyFile() throws IOException {
        // Given
        Path sourceFile = tempDir.resolve("source.txt");
        Path targetFile = tempDir.resolve("target.txt");
        String content = "Hello, World!";
        Files.writeString(sourceFile, content);
        
        // When
        fileUtil.copyFile(sourceFile, targetFile);
        
        // Then
        assertTrue(Files.exists(targetFile));
        assertEquals(content, Files.readString(targetFile));
    }
    
    @Test
    void testCreateDirectories() {
        // Given
        Path directory = tempDir.resolve("nested/deep/directory");
        
        // When
        fileUtil.createDirectories(directory);
        
        // Then
        assertTrue(Files.exists(directory));
        assertTrue(Files.isDirectory(directory));
    }
    
    @Test
    void testExists() throws IOException {
        // Given
        Path existingFile = tempDir.resolve("existing.txt");
        Path nonExistingFile = tempDir.resolve("non-existing.txt");
        Files.createFile(existingFile);
        
        // When & Then
        assertTrue(fileUtil.exists(existingFile));
        assertFalse(fileUtil.exists(nonExistingFile));
    }
    
    @Test
    void testDeleteFile() throws IOException {
        // Given
        Path filePath = tempDir.resolve("test.txt");
        Files.createFile(filePath);
        assertTrue(Files.exists(filePath));
        
        // When
        fileUtil.deleteFile(filePath);
        
        // Then
        assertFalse(Files.exists(filePath));
    }
    
    @Test
    void testGetFileSize() throws IOException {
        // Given
        Path filePath = tempDir.resolve("test.txt");
        String content = "Hello, World!";
        Files.writeString(filePath, content);
        
        // When
        long size = fileUtil.getFileSize(filePath);
        
        // Then
        assertEquals(content.getBytes().length, size);
    }
}
