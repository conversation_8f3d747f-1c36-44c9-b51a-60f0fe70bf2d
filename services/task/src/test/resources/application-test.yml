server:
  port: 0  # 随机端口用于测试

spring:
  application:
    name: task-service-test
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true


mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto

# PCAP导入测试配置
pcap:
  import:
    service-ids: 11,12,13
    base-conf-path: /tmp/test/conf
    thd-env-path: /tmp/test/env
    service-path: /tmp/test/service
    systemd-path: /tmp/test/systemd
    thd-root: /tmp/test/thd
    source-conf-path: /tmp/test/source



# 日志配置
logging:
  level:
    com.geeksec.nta.task: DEBUG

    org.springframework.web: WARN
    org.mybatis: WARN
