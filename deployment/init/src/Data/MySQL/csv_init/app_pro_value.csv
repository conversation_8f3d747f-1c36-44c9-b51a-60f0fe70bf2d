"pro_id","pro_value","pro_name","pro_type","pro_exp","type"
"10000","UNKNOWN","未知应用","未知应用","","1"
"10001","APP_FTP","文件传输协议","文件共享","","1"
"10002","APP_NNTP","新闻阅读器","基础协议","","1"
"10022","APP_WakeOnLan","远端唤醒","基础协议","","1"
"10026","APP_RIP","路由协议-RIP","路由协议","RFC 2453","1"
"10027","APP_IGRP","内部网关路由协议","路由协议","CISCO专有","1"
"10028","APP_EIGRP","增强内部网关路由协议","路由协议","CISCO专有","1"
"10029","APP_OSPF","内部网关协议","路由协议","RFC 2328RFC 5340","1"
"10030","APP_IGP","","","","1"
"10031","APP_EGP","外部网关协议","路由协议","RFC827","1"
"10032","APP_BGP","边界网关协议","路由协议","","1"
"10033","APP_VRRP","虚拟路由冗余协议","路由协议","","1"
"10034","APP_SNMP","简单网络管理协议","网管协议","RFC 2578","1"
"10035","APP_DHCP","动态主机配置协议","网管协议","RFC 2131","1"
"10037","APP_DHCPv6","动态主机配置协议-IPV6","网管协议","","1"
"10040","APP_ICMP_v4","互联网控制报文协议","网管协议","","1"
"10042","APP_ICMP_v6","互联网控制报文协议v6","基础协议","","1"
"10044","APP_ARP","地址解析协议","基础协议","","1"
"10046","APP_NBNS","NetBIOS 名称服务器","基础协议","微软","1"
"10049","APP_NBDGM","Windows的网络数据报输入输出协议","基础协议","微软","1"
"10056","APP_NBSS","NetBIOS 会话服务","基础协议","微软","1"
"10061","APP_SSH","SSH远程控制","远程控制","","1"
"10066","APP_Telnet","telnet远程控制","远程登陆","","1"
"10067","APP_MAIL_SMTP","简单邮件传输协议","邮件协议","","1"
"10069","APP_WHOISDAS","","","","1"
"10070","APP_Tacacs+","终端访问控制器访问控制系统","认证协议","","1"
"10071","APP_DNS","域名访问协议","域名协议","","1"
"10074","APP_Il_DNS","","","","1"
"10093","APP_KERBEROS","Kerberos认证","认证协议","","1"
"10101","APP_MAIL_POP","邮局协议版本3","邮件协议","","1"
"10103","APP_DCERPC","远程过程调用","远程控制","","1"
"10105","APP_MAIL_IMAP","邮件访问协议","邮件协议","","1"
"10113","APP_LDAP","轻量目录访问协议","文件共享","","1"
"10120","APP_CISCOVPN","思科VPN协议","VPN协议","CISCO专有","1"
"10123","APP_SMB","网络文件共享协议","网络基础协议","微软","1"
"10127","APP_SYSLOG","系统日志协议","基础协议","","1"
"10136","APP_RTSP","实时流传输协议","音视频协议","","1"
"10138","APP_VMWARE","Vmware虚拟机","远程控制","","1"
"10145","APP_SOCKS5","网络代理-Socks","匿名网络","","1"
"10146","APP_OPENVPN","OpenVPN协议","VPN协议","","1"
"10148","APP_TDS","应用程序层的协议","数据库协议","","1"
"10149","APP_MSSQL","SQL Server数据库","数据库协议","","1"
"10152","APP_NDPI_CITRIX","Citrix协议","远程控制","","1"
"10154","APP_H323","H323音频","音视频协议","","1"
"10158","APP_MSN","MSN","即时通讯","","1"
"10159","APP_RTMP","RTMP视频","音视频协议","","1"
"10161","APP_SKINNY","SCCP音频","音视频协议","","1"
"10162","APP_NFS","网络文件系统","文件共享","","1"
"10165","APP_MYSQL","MySQL数据库","数据库协议","","1"
"10171","APP_IAX","IAX音频","音视频协议","","1"
"10172","APP_RADMIN","RemoteAdmin远程控制","远程控制","","1"
"10180","APP_POSTGRES","PostgreSQL数据库","数据库协议","","1"
"10184","APP_VNC","虚拟网络控制台","远程控制","","1"
"10188","APP_TEAMVIEWER","TeamViewer远程控制","远程控制","","1"
"10189","APP_XDMCP","显示监控协议","音视频协议","","1"
"10201","APP_ANCP","接入节点控制协议","基础协议","","1"
"10205","APP_TOR","洋葱网络","匿名网络","","1"
"10208","APP_TEAMSPEAK","TeamSpeak语音通讯","音视频协议","","1"
"10209","APP_ENIP","新一代业务平台","接入管理","","1"
"10213","APP_SOPCAST","SopCast广播","P2P","","1"
"10215","APP_ORACLE_TNS","Oracle协议","数据库协议","","1"
"10216","APP_GUILDWARS","","","","1"
"10217","APP_DOFUS","","","","1"
"10221","APP_EDONKEY","电驴","P2P","","1"
"10222","APP_FIESTA","","","","1"
"10226","APP_FILETOPIA","Filetopia文件共享","文件共享","","1"
"10231","APP_STEAM","","","","1"
"10232","APP_SOULSEEK","音乐共享","P2P","","1"
"10233","APP_FCIP","IP光纤通道","基础协议","","1"
"10234","APP_TVANTS","TV蚂蚁","P2P","","1"
"10236","APP_SOCKS4","网络代理-Socks","匿名网络","","1"
"10237","APP_PANDO","Pando文件共享","文件共享","","1"
"10240","APP_CORBA","","","","1"
"10243","APP_MANOLITO","Manolito播放器","音视频协议","","1"
"10244","APP_PVFS","分布式虚拟文件系统","文件共享","","1"
"10245","APP_IMESH","文件共享","P2P","","1"
"10381","APP_NETMAN","网络人远程控制","远程控制","","1"
"10383","APP_ZATTOO","ZATTOO视频","音视频协议","","1"
"10386","APP_ZMQ","消息中间件-","传输层协议","","1"
"10387","APP_MAPLESTORY","","","","1"
"10388","APP_STUN","STUN","基础协议","","1"
"10391","APP_AFP","AppleTalk","苹果专属","","1"
"10392","APP_APPLEJUICE","AppleJuic文件共享","P2P","","1"
"10393","APP_BITBORRENT","比特流","P2P","","1"
"10394","APP_SPOTIFY","声田","音视频协议","","1"
"10395","APP_TVUPLAYER","TVU播放器","音视频协议","","1"
"10396","APP_OSCAR","即时通讯协议","即时通讯","","1"
"10397","APP_RSYNC","数据镜像","基础协议","","1"
"10399","APP_PPSTREAM","PPStream视频","音视频协议","","1"
"10401","APP_POPO","网易泡泡","即时通讯","","1"
"10402","APP_SHOUTCAST","ShoutCast电台","音视频协议","","1"
"10403","APP_STEALTHNET","StealthNet","P2P","","1"
"10404","APP_LOTUS_NOTES","Lotus办公软件","即时通讯","","1"
"10405","APP_SOCRATES","","","","1"
"10406","APP_HTTP_ACTIVESYNC","","","","1"
"10409","APP_MMS","微软流媒体协议","音视频协议","","1"
"10413","APP_NTP","网络时间协议","基础协议","","1"
"10419","APP_NETMAN_NEGOTIATION","网络人远程控制","远程控制","","1"
"10422","APP_IPSec_ISAKMP","IPSec管理协议","VPN协议","","1"
"10429","APP_L2TP","L2TP远程控制","VPN协议","","1"
"10432","APP_RADIUS","Radius认证","认证协议","","1"
"10435","APP_SSDP","简单服务发现协议","基础协议","","1"
"10436","APP_HSRP","热备份路由器协议","基础协议","","1"
"10438","APP_NETFLOW","NetFlow数据交换","基础协议","","1"
"10439","APP_GTP","","","","1"
"10440","APP_GTP_MANAGER","","","","1"
"10443","APP_IPMessage","飞鸽传书","即时通讯","","1"
"10444","APP_MEGACO","媒体网关。","音视频协议","","1"
"10445","APP_XBOX","","","","1"
"10449","APP_Teredo","网络地址转换穿越","基础协议","","1"
"10454","APP_MDNS","组播DNS","基础协议","","1"
"10456","APP_LLMNR","链路本地多播名称解析","基础协议","","1"
"10457","APP_PCANYWHERE","PcAnywhere远程控制","远程控制","","1"
"10458","APP_SFLOW","sFlow","基础协议","","1"
"10459","APP_Tencent_OICQ","腾讯QQ","即时通讯","","1"
"10460","APP_Tencent_MayBe_OICQ","","","","1"
"10464","APP_Tencent_QQMail","QQ邮箱","网络邮箱","","1"
"10465","APP_COLLECTD","主机探针-Collect","基础协议","","1"
"10466","APP_QUAKE","","","","1"
"10473","APP_NOE","","","","1"
"10474","APP_NDPI_ARMAGETRON","","","","1"
"10480","APP_TFTP","简单文件传输协议","文件共享","","1"
"10481","APP_Bootstrap_Protocol","引导程序协议","基础协议","","1"
"10482","APP_KONTIKI","","","","1"
"10486","APP_VIBER","网络电话-Viber","VOIP","","1"
"10487","APP_PPLIVE","PPLive视频","音视频协议","","1"
"10492","APP_HALFLIFE2_AND_MODS","","","","1"
"10497","APP_XT800","快速通远程控制","远程控制","","1"
"10503","APP_SUNLOGIN","向日葵远程控制","远程控制","","1"
"10504","APP_NDPI_BATTLEFIELD","","","","1"
"10508","APP_SKYPE","Skeype","即时通讯","","1"
"10509","APP_HOPOPTS","","","","1"
"10510","APP_IGMP","组管理协议","基础协议","","1"
"10511","APP_GGP","核心网关协议","基础协议","","1"
"10512","APP_STREAM","","","","1"
"10513","APP_CBT","基于核心树的组播路由协议","路由协议","","1"
"10514","APP_BBN_RCC","","","","1"
"10515","APP_NVPII","","","","1"
"10516","APP_PUP","","","","1"
"10517","APP_ARGUS","","","","1"
"10518","APP_EMCON","","","","1"
"10519","APP_XNET","","","","1"
"10520","APP_CHAOS","","","","1"
"10521","APP_MUX","","","","1"
"10522","APP_DCNMEAS","","","","1"
"10523","APP_HMP","","","","1"
"10524","APP_PRM","","","","1"
"10525","APP_IDP","","","","1"
"10526","APP_TRUNK1","","","","1"
"10527","APP_TRUNK2","","","","1"
"10528","APP_LEAF1","","","","1"
"10529","APP_LEAF2","","","","1"
"10530","APP_IRT","","","","1"
"10531","APP_BULK","","","","1"
"10532","APP_MFE_NSP","","","","1"
"10533","APP_MERIT","","","","1"
"10534","APP_DCCP","","","","1"
"10535","APP_3PC","","","","1"
"10536","APP_IDPR","","","","1"
"10537","APP_XTP","","","","1"
"10538","APP_DDP","","","","1"
"10539","APP_CMTP","","","","1"
"10540","APP_TPPP","","","","1"
"10541","APP_IL","","","","1"
"10542","APP_SDRP","","","","1"
"10543","APP_ROUTING","","","","1"
"10544","APP_FRAGMENT","","","","1"
"10545","APP_IDRP","","","","1"
"10546","APP_RSVP","","","","1"
"10548","APP_DSR","","","","1"
"10549","APP_BNA","","","","1"
"10550","APP_INSLP","","","","1"
"10551","APP_SWIPE","","","","1"
"10552","APP_NHRP","","","","1"
"10553","APP_MOBILE","","","","1"
"10554","APP_TLSP","","","","1"
"10555","APP_SKIP","","","","1"
"10556","APP_NONE","","","","1"
"10557","APP_DSTOPTS","","","","1"
"10558","APP_SHIM6_OL","","","","1"
"10559","APP_MIP6","","","","1"
"10560","APP_SATEXPAK","","","","1"
"10561","APP_KRYPTOLA","","","","1"
"10562","APP_RVD","","","","1"
"10563","APP_IPPC","","","","1"
"10564","APP_SATMON","","","","1"
"10565","APP_VISA","","","","1"
"10566","APP_IPCV","","","","1"
"10567","APP_CPNX","","","","1"
"10568","APP_CPHB","","","","1"
"10569","APP_WSN","","","","1"
"10570","APP_PVP","","","","1"
"10571","APP_BRSATMON","","","","1"
"10572","APP_SUNND","","","","1"
"10573","APP_WBMON","","","","1"
"10574","APP_WBEXPAK","","","","1"
"10575","APP_OSI","","","","1"
"10576","APP_VMTP","","","","1"
"10577","APP_SVMTP","","","","1"
"10578","APP_VINES","","","","1"
"10579","APP_TTP","","","","1"
"10580","APP_NSFNETIG","","","","1"
"10581","APP_DGP","","","","1"
"10582","APP_TCF","","","","1"
"10583","APP_SPRITE","","","","1"
"10584","APP_LARP","","","","1"
"10585","APP_MTP","","","","1"
"10586","APP_AX25","","","","1"
"10587","APP_IPINIP","","","","1"
"10588","APP_MICP","","","","1"
"10589","APP_SCCCP","","","","1"
"10590","APP_ETHERIP","","","","1"
"10591","APP_ENCAP","","","","1"
"10592","APP_GMTP","","","","1"
"10593","APP_IFMP","","","","1"
"10594","APP_PNNI","","","","1"
"10595","APP_PIM","","","","1"
"10596","APP_ARIS","","","","1"
"10597","APP_SCPS","","","","1"
"10598","APP_QNX","","","","1"
"10599","APP_AN","","","","1"
"10600","APP_SNP","","","","1"
"10601","APP_COMPAQ","","","","1"
"10602","APP_PGM","","","","1"
"10603","APP_DDX","","","","1"
"10604","APP_IATP","","","","1"
"10605","APP_STP","","","","1"
"10606","APP_SRP","","","","1"
"10607","APP_UTI","","","","1"
"10608","APP_SMP","","","","1"
"10609","APP_SM","","","","1"
"10610","APP_PTP","","","","1"
"10611","APP_ISIS","","","","1"
"10612","APP_FIRE","","","","1"
"10613","APP_CRTP","","","","1"
"10614","APP_CRUDP","","","","1"
"10615","APP_SSCOPMCE","","","","1"
"10616","APP_IPLT","","","","1"
"10617","APP_SPS","","","","1"
"10618","APP_PIPE","","","","1"
"10619","APP_SCTP","","","","1"
"10620","APP_FC","","","","1"
"10621","APP_MPLS","","","","1"
"10622","APP_MANET","","","","1"
"10623","APP_HIP","","","","1"
"10624","APP_SHIM6","","","","1"
"10625","APP_WESP","","","","1"
"10626","APP_ROHC","","","","1"
"10627","APP_AX4000","","","","1"
"10628","APP_NCS","以媒体网关控制协议","VOIP","","1"
"10629","APP_PPTP","PPTP远程控制","VPN协议","","1"
"10630","APP_IPSEC","IPSec协议","VPN协议","","1"
"10631","APP_VOIP","VOIP协议","VOIP","","1"
"10632","APP_HTTPS","","","","1"
"10633","APP_NNTPS","","","","1"
"10634","APP_SMTPS","","","","1"
"10635","APP_IMAPS","","","","1"
"10636","APP_POP3S","","","","1"
"10637","APP_HTTP","超文本传输协议","基础协议","","1"
"10638","APP_SSL","安全套接层","基础协议","","1"
"10639","APP_RDP","远程桌面协议","远程控制","","1"
"10640","APP_Simatic_S7","西门子S7协议","工控协议","","1"
"10641","APP_COTP","面向连接传输协议","工控协议","","1"
"10650","APP_Modbus","","","","1"
"10651","APP_GMSSL","国密协议","","","1"
"10652","APP_FTPS","加密文件传输协议","文件共享","","1"
"10701","No_Payload","无负载","无负载","","1"
"10702","TCP_QueryOnly","TCP仅请求","TCP仅请求","客户端发送SYN，服务器无回应","1"
"10703","TCP_PortClose","TCP端口关闭","TCP端口关闭","客户端发送SYN，服务器发送FIN或RST","1"
"10704","TCP_NoPayload","TCP无负载","TCP无负载","握手正常、挥手正常，没有发送负载数据","1"
"10705","TCP_Unknown","TCP未知协议","TCP未知协议","握手正常、挥手正常，有负载数据，协议未知","1"
"10706","TCP_Other","TCP其他协议","","","1"
"10707","UDP_NoPayload","UDP无负载协议","","","1"
"10708","UDP_Unknown","UDP未知协议","","","1"
