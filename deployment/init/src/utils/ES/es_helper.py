import sys
sys.path.append("./")

import json
import yaml
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
import difflib


def get_es():
    with open("yaml\\ES\\ES.yaml", "r", encoding='gbk') as f:
        config = yaml.safe_load(f)

    # 连接 Elasticsearch
    # 目前不需要密码登录ES，http_auth=(config["elasticsearch"]["http_auth"]["username"], config["elasticsearch"]["http_auth"]["password"])
    es_config = {'host': config["elasticsearch"]["host"], 'port': config["elasticsearch"]["port"], 'scheme': config["elasticsearch"]["scheme"]}
    es = Elasticsearch([es_config])
    return es


# filepath="Data\\ES\\cert_system.json"
def backup_cert_system(es_client, filepath):
    with open(filepath, 'w') as f:
        page = es_client.search(index="cert_system", size=1000, scroll='2m', body={"query": {"match_all": {}}}, request_timeout=600)
        # print(page)
        scroll_size = page['hits']['total']
        if type(scroll_size) == dict:
            scroll_size = scroll_size["value"]
        print(f"[info] load es_client data of size {scroll_size}")
        if scroll_size == 0:
            if '_scroll_id' in page.keys():
                sid = page['_scroll_id']
                es_client.clear_scroll(scroll_id=sid)
            return

        sid = page['_scroll_id']
        datas = [data["_source"] for data in page['hits']['hits']]
        for data in datas:
            f.write(json.dumps(data)+'\n')
        while scroll_size > 0:
            page = es_client.scroll(scroll_id=sid, scroll='2m', request_timeout=600)
            sid = page['_scroll_id']
            scroll_size = len(page['hits']['hits'])
            print(f"[info] load es data of size {scroll_size}")
            datas = [data["_source"] for data in page['hits']['hits']]
            for data in datas:
                f.write(json.dumps(data)+'\n')

        es_client.clear_scroll(scroll_id=sid)
        f.close()


def load_cert_system(es_client):
    count = 0
    with open('Data\\ES\\cert_system.json', 'r') as f:
        for line in f:
            count += 1
            doc = json.loads(line)
            res = es_client.index(index="cert_system", body=doc)
            print(res)
    print(count)


def load_data_bulk(es_client, filepath):
    bulk(es_client, generate_actions(filepath))


def generate_actions(filepath):
    with open(filepath, 'r') as f:
        for line in f:
            doc = json.loads(line)
            yield {'_index': 'cert_system', '_source': doc}
    f.close()


def get_es_data_num(filepath):
    with open(filepath, 'r') as f:
        num = len(f.readlines())
    f.close()
    return num


def delete_data_by_index(es, index):
    query = {
        "query": {
            "match_all": {}
        }
    }
    es.delete_by_query(index=index, body=query)


def get_es_template(es):
    templates = es.indices.get_template()
    print("")


def upload_template(es, name, filepath="Data\\ES\\template\\cert_template.json"):
    with open(filepath) as file:
        template = json.load(file)
    es.indices.put_template(name=name, body=template)
    file.close()


def match_template(es_template, filepath):
    with open(filepath) as file:
        template = json.load(file)
    file.close()
    return difflib.get_close_matches(json.dumps(template), json.dumps(es_template), n=1)

if __name__ == "__main__":
    es = get_es()
    backup_cert_system(es,"Data\\ES\\data\\cert_system_new.json")
    # ES_Client = get_es()
    # load_cert_system_bulk(ES_Client)
    # get_es_template(ES_Client)