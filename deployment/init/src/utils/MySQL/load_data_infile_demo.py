import sys
sys.path.append("./")
import pandas as pd
import yaml
import csv
import pymysql
import datetime
from dbutils.pooled_db import PooledDB


def init_threat_info(pool_th):
    db = pool_th.connection()
    cursor = db.cursor()
    delete_table = """
    DROP TABLE IF EXISTS th_analysis.tb_threat_info
    """
    cursor.execute(delete_table)

    make_table = """
    create table if not exists th_analysis.tb_threat_info
    (
        id          int auto_increment comment '唯一主键ID'
            primary key,
        target      varchar(4096)                      null comment 'hash',
        target_type varchar(100)                       null comment '目标类型(domaincertip)',
        tag_name    varchar(100)                       null comment '目标标签',
        source      varchar(200)                       null comment '来源',
        version     varchar(100)                       null comment '版本',
        shash       varchar(255)                       null comment 'hash',
        create_time datetime default CURRENT_TIMESTAMP null comment '入库时间',
        valid_from  datetime default CURRENT_TIMESTAMP null comment '有效开始时间',
        update_time datetime default CURRENT_TIMESTAMP null comment '更新时间'
    );
    """
    cursor.execute(make_table)

    with open("Data/MySQL/threat_info.csv", "r", encoding='utf-8') as f:
        read = csv.reader(f)
        # 一行一行地存，除去第一行和第一列
        data = list(read)[1:]
        for i in range(len(data)):
            each = data[i]
            each.insert(0, i + 1)
            x = tuple(each)
            # 使用SQL语句添加数据
            sql = "INSERT INTO tb_threat_info VALUES" + str(x)
            cursor.execute(sql)  # 执行SQL语句

        db.commit()  # 提交数据
        cursor.close()  # 关闭游标
        db.close()  # 关闭数据库
        print("tb_threat_info初始化完成")





def load_data_infile(pool_test):
    create_table = """
        CREATE TABLE `tb_threat_info`  (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
        `target` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'hash',
        `target_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标类型(domaincertip)',
        `tag_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标标签',
        `source` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源',
        `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '版本',
        `shash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'hash',
        `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '入库时间',
        `valid_from` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '有效开始时间',
        `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
        PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    """
    db = pool_test.connection()
    cursor = db.cursor()
    cursor.execute(create_table)
    
    data_file = "Data/MySQL/csv_init/tb_threat_info.csv"
    table_name = "tb_threat_info"
    data_sql = f"LOAD DATA LOCAL INFILE '{data_file}' INTO TABLE {table_name} FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\r\\n' IGNORE 1 LINES;"
    print(data_sql)
    cursor.execute(data_sql)
    
    db.commit()  # 提交数据
    cursor.close()  # 关闭游标
    db.close()  # 关闭数据库
    print("tb_threat_info初始化完成")



if __name__=="__main__":
    pool_test = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host="***************",  # 数据库主机地址
        port=23306,  # 数据库端口
        user="root",  # 数据库用户名
        passwd="simpleuse23306p",  # 数据库密码
        database='test',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    load_data_infile(pool_test)
    # init_threat_info(pool_test)