
from kafka.admin import KafkaAdminClient, NewPartitions


def set_partition_num(topic_name, num_partitions, admin_client, logger_info):
    # 获取当前主题的分区数
    # 输出示例[{'error_code': 0, 'topic': 'lmdb', 'is_internal': False, 'partitions': [{'error_code': 0, 'partition': 0, 'leader': 0, 'replicas': [0], 'isr': [0], 'offline_replicas': []}]}]
    topic_metadata = admin_client.describe_topics(topics=[topic_name])
    current_partitions = len(topic_metadata[0]["partitions"])

    # 如果要添加分区，则定义新分区数量和分配方案
    if num_partitions > current_partitions:
        new_partitions = NewPartitions(total_count=num_partitions)
        topic_partitions = {topic_name:new_partitions}
        admin_client.create_partitions(topic_partitions=topic_partitions, validate_only=False)
        logger_info.info(f'{num_partitions - current_partitions} partitions added to topic {topic_name}.')
    else:
        logger_info.info(f'Topic {topic_name} has already had {current_partitions} partitions.')


if __name__ == '__main__':
    set_partition_num("lmdb",4)