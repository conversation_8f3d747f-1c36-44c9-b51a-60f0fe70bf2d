import logging
import time


def get_log_info(name):
    timestamp = int(time.time())
    # 创建 Logger 对象
    logger = logging.getLogger(f"{name}-{timestamp}")

    # 创建 FileHandler 对象，并指定日志输出路径
    handler = logging.FileHandler(f'logger/info/{name}-{timestamp}.log',encoding='utf-8')

    # 创建 Formatter 对象，并设置日志格式
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)

    # 添加 FileHandler 到 Logger 对象
    logger.addHandler(handler)

    # 设置日志级别
    logger.setLevel(logging.INFO)

    return logger


def get_log_error(name):
    timestamp = int(time.time())
    # 创建 Logger 对象
    logger = logging.getLogger(f"{name}-{timestamp}")

    # 创建 FileHandler 对象，并指定日志输出路径
    handler = logging.FileHandler(f'logger/error/{name}-{timestamp}.log',encoding='utf-8')

    # 创建 Formatter 对象，并设置日志格式
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)

    # 添加 FileHandler 到 Logger 对象
    logger.addHandler(handler)

    # 设置日志级别
    logger.setLevel(logging.ERROR)

    return logger


def get_log_warning(name):
    timestamp = int(time.time())
    # 创建 Logger 对象
    logger = logging.getLogger(f"{name}-{timestamp}")

    # 创建 FileHandler 对象，并指定日志输出路径
    handler = logging.FileHandler(f'logger/warning/{name}-{timestamp}.log',encoding='utf-8')

    # 创建 Formatter 对象，并设置日志格式
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)

    # 添加 FileHandler 到 Logger 对象
    logger.addHandler(handler)

    # 设置日志级别
    logger.setLevel(logging.WARNING)

    return logger