# redis connect
redis_conn:
  host: redis
  port: 6379
  db_default: 0
  max_connections: 50
  socket_timeout: 10
  socket_connect_timeout: 5
  password:
    need: FALSE
    username: redis
    pwd: simpleuse6379

# redis_check_by_user
redis_health_check:
  - name: cert_import
    db: 2
    conn_number: 1000
    data_import: 
      import: FALSE
      filepath: NULL
    key_value_requirements:
      check: FALSE
      user:
        type: string
      email:
        type: string
        format: email
  - name: alarm
    db: 1
    conn_number: 2000
    data_import: 
      import: FALSE
      filepath: NULL
    key_value_requirements:
      check: FALSE
      id:
        type: integer
      name:
        type: string
  - name: nebula
    db: 2
    conn_number: 5000
    data_import: 
      import: FALSE
      filepath: NULL
    key_value_requirements:
      check: FALSE
      product_id:
        type: string
      price:
        type: number
        minimum: 0
        maximum: 1000

# redis_pressure_test
redis_pressure_test:
  need: FALSE