# elasticsearch connect
elasticsearch:
  host: elasticsearch
  port: 9200
  scheme: "http"
  http_auth:
    username: elastic
    password: elastic

# ES初始化配置导入
basic_init_db:
  path_to_shell: "Data/ES/es_res.sh"
  path_of_shell: "Data/ES"
  shell_name: "es_res.sh"

# check templates exist
es_templates:
  - name: cert_template
    exists: True
    file: Data/ES/template/cert_template.json
  - name: connect_template
    exists: True
    file: Data/ES/template/connect_template.json
  - name: ssl_template
    exists: True
    file: Data/ES/template/ssl_template.json
  - name: http_template
    exists: True
    file: Data/ES/template/http_template.json
  - name: dns_template
    exists: True
    file: Data/ES/template/dns_template.json
  - name: alarm_template
    exists: True
    file: Data/ES/template/alarm_template.json
  - name: es_index_template
    exists: True
    file: Data/ES/template/es_index_template.json
  - name: ssh_template
    exists: True
    file: Data/ES/template/ssh_template.json

# check index exist
index_exists:
  - name: cert_user
    exists: True
    template_use_own:
      use: False
      template_filepath: Data/ES/template/cert_template.json
    template_use_sys:
      use: True
      template_name: cert_template
    data:
      exists: FALSE
      data_path: NULL
  - name: cert_system
    exists: True
    template_use_own:
      use: False
      template_filepath: Data/ES/template/cert_template.json
    template_use_sys:
      use: True
      template_name: cert_template
    data:
      exists: TRUE
      data_path: Data/ES/data/cert_system.json

# check health
health_check:
  timeout: 60
  retries: 3