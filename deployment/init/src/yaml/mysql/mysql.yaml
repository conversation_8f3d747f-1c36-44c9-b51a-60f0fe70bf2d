mysql_config:
  host: mysql
  port: 3306
  user: root
  password: simpleuse23306p
  product_info: 
    sql: INSERT INTO tb_product_info values("ANALYSIS-TN-02","v2.0.3","W61QE-FUDGQ-OCL33-MS0K7-S9M3M",0)
    db_name: push_database
  basic_init_db:
    path_to_shell: "Data/MySQL/data_init/sql_install.sh"
    path_of_shell: "Data/MySQL/data_init"
    shell_name: "sql_install.sh"
  project_db:
    - project_name: anay
      enabled: True
      knowledge_db:
        - db_name: th_analysis
          tables:
          - name: tb_model_switch
            existence_check:
              query: SELECT 1 FROM tb_model_switch LIMIT 1
              enabled: true
            health_check:
              query: SELECT COUNT(*) FROM tb_model_switch
              row_num: 16
              enabled: true
          - name: tb_alarm_output
            existence_check:
              query: SELECT 1 FROM tb_alarm_output LIMIT 1
              enabled: true
            health_check:
              query: SELECT COUNT(*) FROM tb_alarm_output
              row_num: 2
              enabled: true 
      record_db:
        - db_name: th_analysis
          tables:
          - name: tb_alarm_white
            existence_check:
              query: SELECT 1 FROM tb_alarm_white LIMIT 1
              enabled: true