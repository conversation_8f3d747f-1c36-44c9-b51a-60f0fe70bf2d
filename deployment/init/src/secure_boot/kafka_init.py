import sys
sys.path.append("./")

from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import json
import yaml
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning
from utils.Kafka.kafka_helper import set_partition_num
from kafka.admin import KafkaAdminClient, NewPartitions, NewTopic


def init_kafka():
    # logger 初始化
    logger_info = get_log_info("KAFKA_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("KAFKA_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("KAFKA_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')
    # 读取YAML配置文件
    with open('yaml/kafka/kafka.yaml', 'r') as f:
        config = yaml.safe_load(f)
    f.close()

    for kafka_config in config['kafka']:
        kafka_check(kafka_config,logger_info,logger_error,logger_warning)

    logger_info.info("kafka检查完毕")


def kafka_check(kafka_config,logger_info,logger_error,logger_warning):
    bootstrap_servers = [kafka_config['brokers']]
    # 检查kafka的连接状态
    consumer = KafkaConsumer(bootstrap_servers=bootstrap_servers, group_id=kafka_config['test_consumer']['name'])
    admin_client = KafkaAdminClient(bootstrap_servers=bootstrap_servers)
    try:
        # 使用 describe_cluster() 方法获取集群元数据
        metadata = admin_client.describe_cluster()
        # 获取集群节点数
        node_count = len(metadata['brokers'])
        logger_info.info(f'kafka的节点数为：{node_count}')
    except Exception as e:
        logger_error.error(f"获取kafka节点信息失败，报错内容：{e}")
    finally:
        consumer.close()
    # 对要求的每个topic进行检查
    topics = kafka_config['topics']
    for topic in topics:
        check_topic(topic['name'], topic['partition_num'], bootstrap_servers,
                    logger_info,logger_error, logger_warning,admin_client)


def check_topic(topic_name, topic_partition_num, bootstrap_servers, logger_info,
                logger_error, logger_warning, admin_client):
    # 检查每个topic是否可用，否则初始化
    if topic_name not in admin_client.list_topics():
        topic = NewTopic(name=topic_name, num_partitions=1, replication_factor=1)
        admin_client.create_topics(new_topics=[topic])

    producer = KafkaProducer(bootstrap_servers=bootstrap_servers)
    consumer = KafkaConsumer(topic_name, bootstrap_servers=bootstrap_servers,
                             auto_offset_reset='earliest', enable_auto_commit=False)

    partitions = [TopicPartition(topic_name, partition) for partition in consumer.partitions_for_topic(topic_name)]
    # 检查分区是否匹配数量
    if len(partitions) == topic_partition_num:
        logger_info.info(f"kafka的主题：{topic_name}的分区数为：{topic_partition_num}，符合要求")
    else:
        try:
            set_partition_num(topic_name, topic_partition_num, admin_client, logger_info)
            logger_info.info(f"更改kafka主题为：{topic_name} 的分区数为：{topic_partition_num}成功")
        except Exception as e:
            logger_error.error(f"更改kafka主题为：{topic_name} 的分区数为：{topic_partition_num} 时失败，报错内容为：{e}")
    # 验证每个分区的可用性，消费者+生产者
    for partition in partitions:
        try:
            producer.send(partition.topic, partition=partition.partition)
            consumer.assign([partition])
            logger_info.info(f"测试主题{topic_name}分区{partition}的可用性成功")
        except Exception as e:
            logger_error.error(f"测试主题{topic_name}分区{partition}的可用性时失败。报错内容：{e}")

    producer.close()
    consumer.close()

if __name__ == '__main__':
    init_kafka()
