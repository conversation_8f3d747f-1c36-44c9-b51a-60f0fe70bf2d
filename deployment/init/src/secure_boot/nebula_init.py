import sys
sys.path.append("./")

import yaml
import time
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning
from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config

def init_nebula_graph():
    """初始化Nebula Graph数据库，创建图空间并执行初始化nGQL"""
    
    # 初始化日志记录器
    logger_info = get_log_info("NEBULA_INIT_INFO")
    logger_error = get_log_error("NEBULA_INIT_ERROR")
    logger_warning = get_log_warning("NEBULA_INIT_WARNING")
    
    logger_info.info('='*50 + '\nNebula Graph初始化开始\n' + '='*50)
    
    # 读取配置文件
    try:
        with open("yaml/nebula/nebula.yaml", "r", encoding='utf-8') as f:
            config = yaml.safe_load(f)
            nebula_config = config['nebula']['space']
    except Exception as e:
        logger_error.error(f"读取配置文件失败：{e}")
        raise
    
    # 准备连接参数
    connection_pool = None
    session = None
    
    try:
        # 建立连接
        pool_config = Config()
        pool_config.max_connection_pool_size = 5
        
        connection_pool = ConnectionPool()
        connection_pool.init([
            (nebula_config['connection']['host'], 
             nebula_config['connection']['port'])
        ], pool_config)
        
        session = connection_pool.get_session(
            nebula_config['connection']['user'],
            nebula_config['connection']['password']
        )
        logger_info.info("成功连接到Nebula Graph服务器")
        
        # 创建图空间
        space_name = nebula_config['name']
        create_space_ngql = (
            f'CREATE SPACE IF NOT EXISTS {space_name} '
            '(partition_num=1, replica_factor=1, vid_type=FIXED_STRING(200));'
        )
        
        resp = session.execute(create_space_ngql)
        if resp.is_succeeded():
            logger_info.info(f"图空间 {space_name} 创建成功或已存在")
        else:
            logger_error.error(f"图空间创建失败：{resp.error_msg()}")
            raise Exception("图空间创建失败")
        
        # 等待图空间创建完成
        time.sleep(5)
        logger_info.info("等待图空间创建完成")
        
        # 执行初始化nGQL语句
        with open(nebula_config['init_ngql'], "r", encoding='utf-8') as f:
            ngql_list = f.readlines()
        
        # 切换到目标图空间并执行nGQL语句
        for ngql in ngql_list:
            if ngql.strip():
                full_ngql = f"USE {space_name}; {ngql}"
                logger_info.info(f"执行nGQL：{full_ngql}")
                resp = session.execute(full_ngql)
                if resp.is_succeeded():
                    logger_info.info("执行成功")
                else:
                    logger_error.error(f"执行失败：{resp.error_msg()}")
                    raise Exception("nGQL执行失败")
                
        logger_info.info('='*50 + '\nNebula Graph初始化完成\n' + '='*50)
                
    except Exception as e:
        logger_error.error(f"Nebula Graph初始化失败：{e}")
        raise
    finally:
        # 释放资源
        if session:
            session.release()
        if connection_pool:
            connection_pool.close()

if __name__ == '__main__':
    init_nebula_graph()
