---
- name: yum remove packages
  yum: name={{item}} state=absent
  with_items:
   - "firewalld"

- name: Make sure Download parent directory exists
  file: path={{tmp_download_dir}} state=directory owner=root group=root mode=0755 follow=yes

- name: fetch install files
  get_url: url={{item.url}} dest="{{tmp_download_dir}}/{{item.name}}" validate_certs=no
  with_items:
    - {"name": "rpm-install.tar.gz", "url": "http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/rpm-install.tar.gz"}
    - {"name": "docker-install.tar.gz", "url": "http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/docker-install.tar.gz"}
    - {"name": "nebula-install.tar.gz", "url": "http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/nebula-install.tar.gz"}
    - {"name": "docker-images.zip", "url": "http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/infrastructure-images.zip"}

- name: unarchive install files
  unarchive: src="{{tmp_download_dir}}/{{item}}" dest={{tmp_download_dir}} remote_src=true
  with_items:
    - "rpm-install.tar.gz"
    - "docker-install.tar.gz"
    - "nebula-install.tar.gz"
    # - "docker-images.zip"

- name: Make sure docker images parent directory exists
  file: path="{{tmp_download_dir}}/docker-images" state=directory owner=root group=root mode=0755 follow=yes

- name: unarchive docker images
  unarchive: src="{{tmp_download_dir}}/docker-images.zip" dest="{{tmp_download_dir}}/docker-images" remote_src=true

- name: Finding RPM files
  find:
    paths: "{{ tmp_download_dir }}/rpm-install"
    patterns: "*.rpm"
  register: rpm_result

- name: Install RPM
  yum:
    name: "{{ item.path }}"
    state: present
  with_items: "{{ rpm_result.files }}"
  become: yes
  become_method: sudo

- name: install Docker RPM files
  command:
    chdir: "{{ tmp_download_dir }}/docker-install"
    cmd: rpm -Uvh *.rpm --nodeps --force
  notify: restart docker daemon

###################################################
# - name: Finding Docker RPM files
#   find:
#     paths: "{{ tmp_download_dir }}/docker-install"
#     patterns: "*.rpm"
#   register: docker_rpm_result

# - name: Install Docker RPM
#   yum:
#     name: "{{ item.path }}"
#     disable_gpg_check: true
#     skip_broken: true
#     state: present
#   with_items: "{{ docker_rpm_result.files }}"
#   become: yes
#   become_method: sudo
###################################################

- name: insert iptables rules
  lineinfile:
    path=/etc/sysconfig/iptables
    create=yes
    state=present
    regexp=".*{{ item.protocol }}.*dport.*{{ item.port }}"
    insertafter="^:OUTPUT "
    line="-A INPUT -p {{ item.protocol }} --dport {{ item.port }} -j ACCEPT"
  notify: restart iptables
  with_items:
    - {"protocol": "udp", "port": 7946}
    - {"protocol": "udp", "port": 4789}
    - {"protocol": "tcp", "port": 2376}
    - {"protocol": "tcp", "port": 2377}
    - {"protocol": "tcp", "port": 7946}
    - {"protocol": "tcp", "port": 2181}
    - {"protocol": "tcp", "port": 9559}
    - {"protocol": "tcp", "port": 9669}
    - {"protocol": "tcp", "port": 9779}
    - {"protocol": "tcp", "port": 19559}
    - {"protocol": "tcp", "port": 19669}
    - {"protocol": "tcp", "port": 19779}
    - {"protocol": "tcp", "port": 59000}

- name: Copy file gomplate
  copy:
    src: /opt/GINSTALL/rpm-install/gomplate_linux-amd64-slim
    dest: /usr/local/bin/gomplate
    remote_src: yes
    owner: root
    group: root
    mode: '0755'

- name: Copy file yq
  copy:
    src: /opt/GINSTALL/rpm-install/yq_linux_amd64
    dest: /usr/local/bin/yq
    remote_src: yes
    owner: root
    group: root
    mode: '0755'
