---
- hosts: all
  become: true
  become_user: root
  become_method: sudo
  vars:
    sysctl_settings:
      vm.max_map_count: 655360
      fs.file-max: 655360
      vm.swappiness: 0
      vm.overcommit_memory: 1
      net.ipv4.ip_forward: 1

  tasks:
    - name: set sysctl settings
      sysctl: sysctl_set=yes reload=yes state=present
        name="{{ item.key }}"
        value="{{ item.value }}"
      with_dict: "{{ sysctl_settings }}"
